﻿import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import { Picker } from '@react-native-picker/picker';
import isEqual from 'react-fast-compare';

import { currencyFormat } from '@/utils';

import { useAppDispatch } from '@/store';
import { getPrices } from '@/store/booking';
import { getMember } from '@/store/cart';
import { updateSeatSelected } from '@/store/booking';
import type { IBookingPrice, IBookingSeat } from '@/types/Booking';

interface IPrice {
	price_for_id: string;
	price_for_name: string;
	price: number;
}

const Seat = React.memo(({ seat }: { seat: IBookingSeat }) => {
	const dispatch = useAppDispatch();

	const prices = useSelector(getPrices);
	const member = useSelector(getMember);

	const [price, setPrice] = useState<IPrice>({
		price_for_id: '',
		price_for_name: 'Người lớn',
		price: 0,
	});

	useEffect(() => {
		if (prices.length > 0) {
			const currentPrice = prices.find(
				(item: IBookingPrice) =>
					item.type_seat_id === seat.type_seat_id && item.id === seat.price_id,
			);

			if (currentPrice) {
				setPrice({
					...currentPrice,
					price_for_id: currentPrice.price_for_id.toString(),
				});
			} else {
				const keyName = member ? 'Người lớn Member' : 'Người lớn';
				const defaultPrice = prices.find(
					(item: IBookingPrice) =>
						item.type_seat_id === seat.type_seat_id &&
						item.price_for_name === keyName,
				);
				if (defaultPrice) {
					setPrice({
						...defaultPrice,
						price_for_id: defaultPrice.price_for_id.toString(),
					});
					dispatch(
						updateSeatSelected({
							seat: seat.id,
							price_id: defaultPrice.id,
							price: defaultPrice.price,
						}),
					);
				}
			}
		}
	}, [seat, prices, member]); // eslint-disable-line react-hooks/exhaustive-deps

	const availablePrice = useMemo(() => {
		return prices.filter(
			(item: IBookingPrice) => item.type_seat_id === seat.type_seat_id,
		);
	}, [prices, seat.type_seat_id]);

	const changePrice = (value: string) => {
		const currentPrice = prices.find(
			(item: IBookingPrice) =>
				item.price_for_id === parseInt(value) &&
				item.type_seat_id === seat.type_seat_id,
		);
		if (currentPrice) {
			dispatch(
				updateSeatSelected({
					seat: seat.id,
					price_id: currentPrice.id,
					price: currentPrice.price,
				}),
			);
		}
	};

	return (
		<View style={styles.row}>
			<View style={styles.seat}>
				<Text style={styles.seatName}>{seat.name}</Text>
			</View>
			<View style={styles.type}>
				<Picker
					selectedValue={price ? price.price_for_id : ''}
					onValueChange={changePrice}
					style={styles.picker}>
					{availablePrice.map((item: IBookingPrice) => (
						<Picker.Item
							label={item.price_for_name}
							value={item.price_for_id}
							key={seat.id + item.price_for_id}
						/>
					))}
				</Picker>
			</View>
			<View style={styles.price}>
				<Text style={styles.priceValue}>{currencyFormat(price.price)}</Text>
			</View>
		</View>
	);
}, isEqual);

Seat.displayName = 'BookingSidebarSeat';

const styles = StyleSheet.create({
	row: {
		flexDirection: 'row',
		alignItems: 'center',
		marginVertical: 3,
		paddingLeft: 10,
		paddingRight: 5,
	},
	seat: {
		width: 35,
	},
	seatName: {
		fontWeight: 'bold',
	},
	price: {
		width: 55,
	},
	priceValue: {
		textAlign: 'right',
	},
	type: {
		flex: 1,
	},
	picker: {
		height: 32,
		borderWidth: 0,
		borderColor: 'transparent',
		fontSize: 12,
	},
});

export default Seat;
