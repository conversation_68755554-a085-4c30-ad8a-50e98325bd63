{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 14495284417331760173, "deps": [[3060637413840920116, "proc_macro2", false, 13520058446541265646], [3150220818285335163, "url", false, 14317979308849973313], [4899080583175475170, "semver", false, 2426471561692137791], [7170110829644101142, "json_patch", false, 3739604338288812391], [7392050791754369441, "ico", false, 6683096433856259815], [8319709847752024821, "uuid", false, 15916998633034192982], [9689903380558560274, "serde", false, 17710615169291184661], [9857275760291862238, "sha2", false, 16114715544703283230], [10640660562325816595, "syn", false, 617622337898976511], [10806645703491011684, "thiserror", false, 11115062776651022174], [11050281405049894993, "tauri_utils", false, 8694799301641733153], [12687914511023397207, "png", false, 13832981976275846070], [13077212702700853852, "base64", false, 10148683486352841222], [14132538657330703225, "brotli", false, 396834054790866260], [15367738274754116744, "serde_json", false, 4006064592906587583], [15622660310229662834, "walkdir", false, 11897401627305349443], [17990358020177143287, "quote", false, 1666506227630477606]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-afabb45282412e93\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}