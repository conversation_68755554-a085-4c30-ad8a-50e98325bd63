import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import isEqual from 'react-fast-compare';
import Separator from '@/components/Separator';
import FlatList from '@/components/flatlist';
import { ICartTicket } from '@/types/Cart';
import Ticket from './Ticket';

type Props = {
	data: ICartTicket[];
	type: string;
};

const Tickets = React.memo(({ data, type }: Props) => {
	const renderItem = ({ item }: { item: ICartTicket }) => (
		<Ticket ticket={item} type={type} />
	);
	return (
		<View style={styles.bg}>
			<View style={styles.header}>
				<Text style={styles.title}>V� ({data.length})</Text>
			</View>
			<View style={styles.list}>
				<FlatList
					data={data}
					keyExtractor={(item) => item.id}
					renderItem={renderItem}
					ItemSeparatorComponent={Separator}
				/>
			</View>
		</View>
	);
}, isEqual);

Tickets.displayName = 'PrintTickets';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	header: {
		backgroundColor: '#ddd',
		paddingHorizontal: 15,
		paddingVertical: 4,
	},
	title: {
		fontSize: 17,
	},
	list: {
		flex: 1,
		backgroundColor: '#fff',
	},
});

export default Tickets;
