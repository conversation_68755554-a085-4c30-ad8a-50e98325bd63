﻿import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';

import { getProducts, getTickets } from '@/store/cart/Selector';
import { RootState } from '@/store';
import ListTickets from './components/ListTickets';
import ListProducts from './components/ListProducts';

const Product = React.memo(() => {
	const tickets = useSelector(getTickets);
	const products = useSelector(getProducts);
	const gifts = useSelector((state: RootState) => state.cart.gifts);

	return (
		<View style={styles.wrap}>
			{tickets.length > 0 && <ListTickets data={tickets} />}
			{(products.length > 0 || gifts.length > 0) && (
				<ListProducts data={[...products, ...gifts]} />
			)}
		</View>
	);
});

Product.displayName = 'CheckoutProduct';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
		marginHorizontal: 10,
		marginVertical: 5,
		borderColor: '#eee',
		borderWidth: 1,
		borderRadius: 5,
	},
});

export default Product;
