import { IMemberState } from '@/types/Member';
import { createSlice } from '@reduxjs/toolkit';
import { RootState } from '..';
import { fectchData } from './AsyncThunks';

const initialState: IMemberState = {
	data: [],
	links: {},
	meta: {},
};

export const memberSlice = createSlice({
	name: 'member',
	initialState,
	reducers: {
		updateMember: (state, action) => {
			const index = state.data.findIndex(
				// eslint-disable-next-line eqeqeq
				(item) => item.id == action.payload.id,
			);
			if (index >= 0) {
				state.data[index] = action.payload.data;
			}
		},
	},
	extraReducers: (builder) => {
		builder.addCase(fectchData.fulfilled, (state, action) => {
			if (action.payload && action.payload.data) {
				state.data = action.payload.data;
				state.links = action.payload.links;
				state.meta = action.payload.meta;
			}
		});
	},
});

export const { updateMember } = memberSlice.actions;

export const getData = (state: RootState) => state.member.data;

export default memberSlice.reducer;
