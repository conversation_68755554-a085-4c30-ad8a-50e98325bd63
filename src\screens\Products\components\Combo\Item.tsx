﻿import React, { memo } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { IProduct } from '@/screens/products/types';
import { useAppDispatch } from '@/store';
import { addComboItem } from '@/store/product';
import primary from '@/constants/Colors';

interface IProductItem {
	data: IProduct;
}

const Item = memo(({ data }: IProductItem) => {
	const dispatch = useAppDispatch();

	const onPress = () => {
		dispatch(
			addComboItem({
				type: data.quantity > 0 ? 'decrement' : 'increment',
				item: data.id,
			}),
		);
	};

	return (
		<View style={[styles.item, data.quantity > 0 ? styles.active : null]}>
			<TouchableOpacity onPress={onPress} style={styles.itemContainer}>
				<Image
					source={{
						uri: data.image,
					}}
					style={styles.image}
				/>
				<Text style={styles.text}>{data.name}</Text>
			</TouchableOpacity>
		</View>
	);
});

Item.displayName = 'ProductItem';

const styles = StyleSheet.create({
	item: {
		flex: 1,
		width: 120,
		flexDirection: 'column',
		margin: 6,
		backgroundColor: '#fff',
		borderRadius: 5,
		borderColor: 'transparent',
		borderWidth: 2,
		paddingVertical: 5,
		paddingHorizontal: 10,
		elevation: 3,
	},
	active: {
		borderColor: primary,
	},
	itemContainer: {
		alignItems: 'center',
		width: '100%',
		flex: 1,
	},
	text: {
		textAlign: 'center',
		width: '100%',
		fontWeight: 'bold',
	},
	boxPrice: {
		flex: 1,
		width: '100%',
		justifyContent: 'flex-end',
	},
	image: {
		width: 80,
		height: 80,
		borderRadius: 80,
	},
	colAction: {
		flexDirection: 'row',
		borderTopWidth: 1,
		borderTopColor: '#ddd',
		marginTop: 5,
		paddingTop: 5,
	},
	quantity: {
		width: 35,
		fontSize: 20,
		lineHeight: 30,
		textAlign: 'center',
	},
	btn: {
		borderColor: '#ddd',
		borderWidth: 1,
		width: 35,
		height: 35,
		borderRadius: 4,
	},
	btnText: {
		textAlign: 'center',
		fontSize: 30,
		lineHeight: 30,
	},
	btnDes: {},
});

export default Item;
