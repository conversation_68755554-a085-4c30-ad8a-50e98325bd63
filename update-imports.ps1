# Script để cập nhật tất cả import paths sau khi đổi tên thư mục

Write-Host "Bắt đầu cập nhật import paths..."

# Danh sách các thay đổi import path
$importMappings = @{
    # Components
    "@/components/Alert" = "@/components/alert"
    "@/components/Button" = "@/components/button"
    "@/components/CinemaScreen" = "@/components/cinema-screen"
    "@/components/FlatList" = "@/components/flat-list"
    "@/components/Image" = "@/components/image"
    "@/components/Input" = "@/components/input"
    "@/components/Loading" = "@/components/loading"
    "@/components/MemberCard" = "@/components/member-card"
    "@/components/Menu" = "@/components/menu"
    "@/components/Modal" = "@/components/modal"
    "@/components/ScrollView" = "@/components/scroll-view"
    "@/components/Topbar" = "@/components/topbar"
    
    # Screens
    "@/screens/Booking" = "@/screens/booking"
    "@/screens/Checkout" = "@/screens/checkout"
    "@/screens/Exchange" = "@/screens/exchange"
    "@/screens/Members" = "@/screens/members"
    "@/screens/Print" = "@/screens/print"
    "@/screens/Products" = "@/screens/products"
    "@/screens/Reports" = "@/screens/reports"
    "@/screens/Tickets" = "@/screens/tickets"
    "@/screens/Timelines" = "@/screens/timelines"
    "@/screens/Welcome" = "@/screens/welcome"
    
    # Store
    "@/store/App" = "@/store/app"
    "@/store/Booking" = "@/store/booking"
    "@/store/Cart" = "@/store/cart"
    "@/store/Member" = "@/store/member"
    "@/store/Product" = "@/store/product"
}

# Lấy tất cả file TypeScript và JavaScript
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts", "*.tsx", "*.js", "*.jsx" | Where-Object { $_.FullName -notmatch "node_modules" }

$totalFiles = $files.Count
$processedFiles = 0

Write-Host "Tìm thấy $totalFiles files để xử lý..."

foreach ($file in $files) {
    $processedFiles++
    $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
    Write-Progress -Activity "Cập nhật import paths" -Status "Đang xử lý: $relativePath" -PercentComplete (($processedFiles / $totalFiles) * 100)
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $hasChanges = $false
    
    # Thay thế từng mapping
    foreach ($mapping in $importMappings.GetEnumerator()) {
        $oldPath = $mapping.Key
        $newPath = $mapping.Value
        
        # Thay thế các pattern import khác nhau
        $patterns = @(
            "from `"$oldPath`"",
            "from '$oldPath'",
            "import\(`"$oldPath`"\)",
            "import\('$oldPath'\)"
        )
        
        foreach ($pattern in $patterns) {
            $newPattern = $pattern.Replace($oldPath, $newPath)
            if ($content -match [regex]::Escape($pattern)) {
                $content = $content -replace [regex]::Escape($pattern), $newPattern
                $hasChanges = $true
            }
        }
        
        # Thay thế các import với subpath
        $subpathPattern = "$oldPath/"
        $newSubpathPattern = "$newPath/"
        if ($content.Contains($subpathPattern)) {
            $content = $content.Replace($subpathPattern, $newSubpathPattern)
            $hasChanges = $true
        }
    }
    
    # Ghi lại file nếu có thay đổi
    if ($hasChanges) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        Write-Host "✓ Đã cập nhật: $relativePath"
    }
}

Write-Progress -Activity "Cập nhật import paths" -Completed

Write-Host "`nHoàn thành cập nhật import paths!"
Write-Host "Kiểm tra lại với ESLint..."

# Chạy ESLint để kiểm tra
try {
    $eslintResult = npx eslint src --quiet --fix 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ ESLint: Không có lỗi"
    } else {
        Write-Host "⚠ ESLint vẫn báo một số lỗi:"
        Write-Host $eslintResult
    }
} catch {
    Write-Host "⚠ Không thể chạy ESLint: $_"
}

Write-Host "`nCập nhật import paths hoàn tất!"
