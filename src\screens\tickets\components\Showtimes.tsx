import React from 'react';
import { StyleSheet, View } from 'react-native';
import isEqual from 'react-fast-compare';

import FlatList from '@/components/flatlist';
import Showtime from './Showtime';
import { IShowtimes, IShowtime } from '../types';

const Showtimes = React.memo(({ data }: IShowtimes) => {
	const renderItem = ({ item }: { item: IShowtime }) => (
		<Showtime
			showtime={item}
			poster={data.poster}
			name={`${data.name} (${data.age || ''})`}
			age={data.age}
		/>
	);

	return (
		<View style={styles.showtimes}>
			<FlatList
				data={data.showtime.filter((item: IShowtime) => item.status)}
				renderItem={renderItem}
				numColumns={4}
				keyExtractor={(item) => item.id}
			/>
		</View>
	);
}, isEqual);

Showtimes.displayName = 'Showtimes';

const styles = StyleSheet.create({
	showtimes: {
		paddingHorizontal: 5,
	},
});

export default Showtimes;
