{"rustc": 10895048813736897673, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 11453919174981910577, "deps": [[2883436298747778685, "pki_types", false, 954979829807540393], [3722963349756955755, "once_cell", false, 5493430054469386965], [5491919304041016563, "ring", false, 17994817621532769364], [6528079939221783635, "zeroize", false, 297386047092486888], [7161480121686072451, "build_script_build", false, 15018052638724748814], [17003143334332120809, "subtle", false, 6501602761881611215], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 7336658396509410807]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-4058a75e383f3e11\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}