import { createAsyncThunk } from '@reduxjs/toolkit';
import { router } from 'expo-router';

import { setProducts } from '@/store/Product/Slice';
import { setSeats } from '@/store/Booking/Slice';
import cartAPI from '@/services/Cart';
import { setMoney } from '@/store/Cart';
import { setLoading } from '@/store/App';
import { withQrCode } from '@/services/Kiosk';

export type CreateOrderParams = {
	tickets: any[];
	combo: any[];
	previos_screen?: string;
};

export const createOrder = createAsyncThunk(
	'cart/createOrder',
	async (args: CreateOrderParams, thunkApi) => {
		thunkApi.dispatch(setLoading(true));
		try {
			const response = await cartAPI.createOrder(args);
			if (response.order_id) {
				router.navigate({
					pathname: '/pos/checkout',
					params: {
						previous_screen: args.previos_screen,
					},
				});
			}
			thunkApi.dispatch(setLoading(false));
			return response;
		} catch (e) {
			console.log(e);
		}
		thunkApi.dispatch(setLoading(false));
		return thunkApi.rejectWithValue('Error');
	},
);

export type SetMemberOrderParams = {
	member: string;
	order: number;
};

export const setMemberOrder = createAsyncThunk(
	'cart/setMemberOrder',
	async (args: SetMemberOrderParams) => {
		try {
			const response = await cartAPI.setMember(args);
			return response;
		} catch (e) {
			console.log(e);
		}
	},
);

export type QrCodeProps = {
	orderId: number;
	amount: number;
	gateway: string;
};

export const payWithQrCode = createAsyncThunk(
	'cart/payWithQrCode',
	async (args: QrCodeProps, thunkApi) => {
		thunkApi.dispatch(setLoading(true));
		try {
			const response = await withQrCode(args);
			thunkApi.dispatch(setLoading(false));
			return response;
		} catch (e) {
			console.log(e);
		}
		thunkApi.dispatch(setLoading(false));
		return thunkApi.rejectWithValue('Không thể tạo mã QR');
	},
);

export const clearOrder = createAsyncThunk(
	'cart/clearOrder',
	async (args, thunkApi) => {
		thunkApi.dispatch(setProducts([]));
		thunkApi.dispatch(setSeats([]));
		thunkApi.dispatch(setMoney(0));
		return;
	},
);
