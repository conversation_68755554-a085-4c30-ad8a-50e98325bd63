import { useState } from 'react';
import Icon from '@expo/vector-icons/Ionicons';

import MenuItem from './MenuItem';
import { toggleFullScreen } from '@/modules/pos';

const ButtonFullscreen = () => {
	const [isFull, setFull] = useState(false);

	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon
			size={size}
			name={isFull ? 'contract-outline' : 'expand-outline'}
			color={color}
		/>
	);

	const onPress = () => {
		toggleFullScreen();
		setFull(!isFull);
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Fullscreen'}
			isFocused={false}
		/>
	);
};

export default ButtonFullscreen;
