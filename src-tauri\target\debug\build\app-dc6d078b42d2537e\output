cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=E:\touchcinema\touchcinema-pos\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=pos
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_touchcinema
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=E:\touchcinema\touchcinema-pos\src-tauri\target\debug\build\app-dc6d078b42d2537e\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
cargo:rerun-if-changed=TwPrinter-x86_64-pc-windows-msvc.exe
cargo:rerun-if-changed=cashdrawer\cashdrawer.exe
cargo:rerun-if-changed=cashdrawer\cc3280.dll
cargo:rerun-if-changed=cashdrawer\inpout32.dll
cargo:rerun-if-changed=logo-ticket.png
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=E:\touchcinema\touchcinema-pos\src-tauri\target\debug\build\app-dc6d078b42d2537e\out\resource.lib
