{"rustc": 10895048813736897673, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 1101043664248481667, "deps": [[3060637413840920116, "proc_macro2", false, 13520058446541265646], [7341521034400937459, "tauri_codegen", false, 13055517590349564296], [10640660562325816595, "syn", false, 617622337898976511], [11050281405049894993, "tauri_utils", false, 8694799301641733153], [13077543566650298139, "heck", false, 290801593150339784], [17990358020177143287, "quote", false, 1666506227630477606]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-02ee1cd205ae8915\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}