import React, { useEffect, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

import { getTimelines, setTimelines } from '@/store/booking/Slice';
import { getSchedules } from '@/services/Booking';
import { ITabItem, TabsProps } from '../types';
import TabItem from './TabItem';
import ScrollView from '@/components/scrollview';

const Tabs = React.memo(({ active, onPress = () => {} }: TabsProps) => {
	const dispatch = useDispatch();

	const data = useSelector(getTimelines);

	useEffect(() => {
		if (data.length === 0) {
			getData();
		}
	}, [data]); // eslint-disable-line react-hooks/exhaustive-deps

	const getData = async () => {
		try {
			const res = await getSchedules();
			if (res && res.length > 0) {
				dispatch(setTimelines(res));
			}
		} catch (e) {
			console.log(e);
		}
	};

	const tabs = useMemo(() => {
		const time = new Date();
		return data.map((item: ITabItem) => {
			let title, date, day, month;
			const parse = item.date.split(',');
			title = parse[0];
			if (parse[1]) {
				const parseMonth = parse[1].split('/');
				month = 'Th�ng ' + parseMonth[1];
				day = parseMonth[0];
				if (parseMonth.length === 2) {
					date = parse[1].trim() + '/' + time.getFullYear();
				} else {
					date = parse[1].trim();
				}
			}
			return {
				title,
				day,
				month,
				date,
			};
		});
	}, [data]);

	return (
		<ScrollView horizontal>
			<View style={styles.tabs}>
				{tabs.map((item: any) => (
					<TabItem
						data={item}
						key={item.date}
						onPress={onPress}
						active={active === item.date}
					/>
				))}
			</View>
		</ScrollView>
	);
});

Tabs.displayName = 'Tabs';

const styles = StyleSheet.create({
	tabs: {
		flexDirection: 'row',
	},
});

export default Tabs;
