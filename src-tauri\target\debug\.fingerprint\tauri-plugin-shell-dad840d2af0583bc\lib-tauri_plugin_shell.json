{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 10146951170256647434, "deps": [[500211409582349667, "shared_child", false, 2026888186536179911], [1582828171158827377, "build_script_build", false, 18186587240510745694], [5986029879202738730, "log", false, 14669300167346157817], [9451456094439810778, "regex", false, 5575854039059109283], [9538054652646069845, "tokio", false, 17307833443289394789], [9689903380558560274, "serde", false, 10697150404045262645], [10755362358622467486, "tauri", false, 17704364160439666055], [10806645703491011684, "thiserror", false, 11115062776651022174], [11337703028400419576, "os_pipe", false, 14787950532675565218], [14564311161534545801, "encoding_rs", false, 13133722112187812780], [15367738274754116744, "serde_json", false, 12445351040836002554], [16192041687293812804, "open", false, 15368201625406175945]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-dad840d2af0583bc\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}