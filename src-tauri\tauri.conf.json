{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "TouchCinema POS", "mainBinaryName": "TouchCinema POS", "version": "../package.json", "identifier": "com.touchcinema.pos", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:8081", "beforeDevCommand": "pnpm start", "beforeBuildCommand": "npx expo export --platform web"}, "app": {"windows": [{"title": "TouchCinema POS", "width": 1024, "height": 768, "resizable": true, "fullscreen": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "category": "Business", "copyright": "Mr<PERSON><PERSON><PERSON>", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "externalBin": ["TwPrinter"], "resources": ["cashdrawer", "logo-ticket.png"], "createUpdaterArtifacts": true}, "plugins": {"updater": {"endpoints": ["https://u.mrtaiw.dev/api/check-update?app-id=com.touchcinema.pos"], "dialog": false, "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDVGNDM4NENDQjdCRDU5OTcKUldTWFdiMjN6SVJEWDU5bEZQUXZ5UUhEL3k3dnpmS2Q4ZWZYbzBtdnVGczY0TUFzSXFIR1NUN3EK"}}}