﻿import React, { useMemo, memo, useCallback, useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { router, usePathname } from 'expo-router';

import Button from '@/components/button';
import Separator from '@/components/Separator';
import MemberButton from '@/components/button/Member';
import HoldButton from '@/screens/booking/components/HoldButton';
import Alert from '@/components/alert';

import { useAppDispatch } from '@/store';
import { setSeats } from '@/store/booking/Slice';
import { getSelected, getPrices } from '@/store/booking/Selector';
import { CreateOrderParams, createOrder, setMember } from '@/store/cart';
import { getProducts, setProducts } from '@/store/product';

import { reset as resetSeats } from '@/services/Booking';
import sendToCustomer from '@/services/Broadcast';

import { showMessage } from '@/utils/Toast';
import Shortcut from '@/utils/Shortcut';
import { currencyFormat, isCustomer } from '@/utils';

import type { IBookingPrice, IBookingSeat } from '@/types/Booking';

type Props = {
	ticket?: boolean;
	children?: string | JSX.Element | JSX.Element[];
};

const BoxAction = memo(({ ticket, children }: Props) => {
	const dispatch = useAppDispatch();

	const pathname = usePathname();

	const prices = useSelector(getPrices);
	const products = useSelector(getProducts);
	const seats = useSelector(getSelected);

	const isModal = pathname.includes('booking-products');

	const isCustomerScreen = isCustomer();

	useEffect(() => {
		sendToCustomer({
			action: 'setProducts',
			payload: {
				seats,
				products,
			},
		});
		if (!isCustomerScreen) {
			Shortcut.bind('F1', onCheckout);
			return () => {
				Shortcut.unbind('F1');
			};
		}
	}, [seats, products, isCustomerScreen]); // eslint-disable-line react-hooks/exhaustive-deps

	const totalProduct = useMemo(() => {
		if (products.length > 0) {
			return products
				.map((item) => (item.price ? item.price : 0) * item.quantity)
				.reduce((prev: number, next: number) => prev + next);
		}
		return 0;
	}, [products]);

	const totalTicket = useMemo(() => {
		if (seats.length > 0) {
			return seats
				.map((seat: IBookingSeat) => {
					return (
						prices.find(
							(item: IBookingPrice) =>
								item.type_seat_id === seat.type_seat_id &&
								item.id === seat.price_id,
						)?.price || 0
					);
				})
				.reduce((prev: number, next: number) => prev + next);
		}
		return 0;
	}, [seats, prices]);

	const total = useMemo(() => {
		return totalProduct + totalTicket;
	}, [totalProduct, totalTicket]);

	const reset = useCallback(() => {
		resetSeats();
		dispatch(setProducts([]));
		dispatch(setSeats([]));
	}, []); //	eslint-disable-line react-hooks/exhaustive-deps

	const onCheckout = async () => {
		if (seats.length === 0 && products.length === 0) {
			showMessage({
				message: 'Vui lòng chọn trước khi thanh toán',
				type: 'danger',
			});
			return;
		}

		const formData: CreateOrderParams = {
			tickets: [],
			combo: [],
			previos_screen: pathname.replace('/pos/booking-products', '/pos/booking'),
		};
		seats.forEach((seat: IBookingSeat) =>
			formData.tickets.push({
				seat_id: seat.id,
				price_id: seat.price_id,
			}),
		);
		products.forEach((item) =>
			formData.combo.push({
				combo_id: item.id,
				quantity: item.quantity,
			}),
		);
		dispatch(createOrder(formData));
	};

	const onCancel = useCallback(() => {
		if (totalTicket > 0) {
			Alert.alert(
				'Quay lại',
				'Đang có ghế đang chọn, bọn có muốn giữ ghế hiện tại?',
				[
					{
						text: 'Không',
						onPress: backWithReset,
					},
					{
						text: 'Giữ ghế',
						onPress: router.back,
					},
				],
			);
		} else {
			dispatch(setMember(null));
			router.back();
		}
	}, [totalTicket]); // eslint-disable-line react-hooks/exhaustive-deps

	const backWithReset = () => {
		resetSeats();
		dispatch(setSeats([]));
		dispatch(setMember(null));
		router.back();
	};

	const toProducts = useCallback(() => {
		router.navigate({
			pathname: '/pos/booking-products',
		});
	}, []);

	const toBooking = useCallback(() => {
		router.back();
	}, []);

	return (
		<View>
			<Separator />
			{ticket && (
				<>
					{children}
					<View style={styles.attr}>
						<Text style={styles.text}>Tiền vé:</Text>
						<Text style={styles.value}>{currencyFormat(totalTicket)} Đ</Text>
					</View>
					<View style={styles.attr}>
						<Text style={styles.text}>Tiền bắp nước:</Text>
						<Text style={styles.value}>{currencyFormat(totalProduct)} Đ</Text>
					</View>
				</>
			)}
			<View style={styles.attr}>
				<Text style={styles.text}>Tổng cộng:</Text>
				<Text style={styles.value}> {currencyFormat(total)} Đ</Text>
			</View>
			{!isCustomerScreen && (
				<View style={styles.action}>
					{ticket && (
						<>
							<View style={styles.row}>
								<Button
									style={styles.btn}
									onPress={onCancel}
									text="Chọn phim"
									textStyle={styles.btnText}
								/>
								<Button
									onPress={reset}
									style={styles.btn}
									text="Chọn lại"
									textStyle={styles.btnText}
								/>
							</View>
							<View style={styles.row}>
								<Button
									style={styles.btn}
									onPress={toProducts}
									text="Bắp nước"
									textStyle={styles.btnText}
								/>
								<MemberButton
									style={styles.btnFix}
									text="KHTT"
									textStyle={styles.btnText}
								/>
							</View>
						</>
					)}
					{isModal ? (
						<View style={styles.row}>
							<Button
								onPress={toBooking}
								style={styles.btn}
								text="Chọn ghế"
								textStyle={styles.btnText}
							/>
							<Button
								onPress={onCheckout}
								style={styles.btn}
								text="Thanh toán (F1)"
								textStyle={styles.btnText}
							/>
						</View>
					) : (
						<View style={styles.row}>
							{ticket && (
								<HoldButton style={styles.btn} textStyle={styles.btnText} />
							)}
							<Button
								onPress={onCheckout}
								style={styles.btn}
								text="Thanh toán (F1)"
								textStyle={styles.btnText}
							/>
						</View>
					)}
				</View>
			)}
			{isCustomerScreen && <View style={styles.bottom} />}
		</View>
	);
});

BoxAction.displayName = 'BoxAction';

const styles = StyleSheet.create({
	action: {
		margin: 3,
	},
	btn: {
		flex: 1,
		margin: 3,
		marginHorizontal: 3,
		paddingHorizontal: 4,
		justifyContent: 'center',
	},
	btnFix: {
		flex: 1,
		margin: 3,
		marginHorizontal: 3,
		paddingHorizontal: 4,
		justifyContent: 'center',
		height: 40,
	},
	btnText: {
		color: '#fff',
		textAlign: 'center',
		fontSize: 14,
		fontWeight: 'bold',
		textTransform: 'uppercase',
	},
	row: {
		flexDirection: 'row',
	},
	attr: {
		marginHorizontal: 10,
		flexDirection: 'row',
		justifyContent: 'space-between',
	},
	text: {
		color: '#000',
		fontSize: 16,
	},
	value: {
		fontWeight: 'bold',
		fontSize: 16,
	},
	bottom: {
		height: 8,
	},
});

export default BoxAction;
