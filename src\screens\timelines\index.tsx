import React, {
	useCallback,
	useEffect,
	useLayoutEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useDispatch } from 'react-redux';

import { showMessage } from '@/utils/Toast';

import { getStatistics } from '@/services/Booking';
import Room from './components/Room';
import Tabs from './components/Tabs';
import type { Room as RoomType } from './types';
import ScrollView from '@/components/ScrollView';
import { setLoading } from '@/store/App';

const hours = [
	'07',
	'08',
	'09',
	'10',
	'11',
	'12',
	'13',
	'14',
	'15',
	'16',
	'17',
	'18',
	'19',
	'20',
	'21',
	'22',
	'23',
	'24',
];

const time = new Date();

const months = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];

const Timelines = () => {
	const dispatch = useDispatch();

	const ref = useRef<View>(null);

	const [data, setData] = useState<RoomType[]>([]);
	const [current, setCurrent] = useState(
		`${time.getDate()}/${months[time.getMonth()]}/${time.getFullYear()}`,
	);
	const [cellWidth, setCellWidth] = useState(60);

	useEffect(() => {
		getData();
	}, [current]); // eslint-disable-line react-hooks/exhaustive-deps

	const getData = async () => {
		dispatch(setLoading(true));
		try {
			const res = await getStatistics(current);
			if (Array.isArray(res)) {
				setData(res);
			}
		} catch (e) {
			console.log(e);
			showMessage({
				message: 'Lỗi khi lấy thông tin lịch chiếu, vui lòng thử lại',
			});
		}
		dispatch(setLoading(false));
	};

	const renderTime = useMemo(() => {
		return hours.map((item) => (
			<View key={'h-' + item} style={styles.hour}>
				<Text style={styles.hourLabel}>{item}</Text>
			</View>
		));
	}, []);

	useLayoutEffect(() => {
		ref.current?.measureInWindow((_, __, width, ___) => {
			setCellWidth(width / hours.length);
		});
	}, [ref]);

	const onPress = useCallback((date: string) => {
		setCurrent(date);
	}, []);

	return (
		<View style={styles.bg}>
			<View style={styles.action}>
				<View style={styles.date}>
					<Tabs onPress={onPress} active={current} />
				</View>
			</View>

			<View style={styles.calendar}>
				<ScrollView horizontal>
					<View style={styles.scrollInner}>
						<View style={styles.header} ref={ref}>
							{renderTime}
						</View>
						{data.map((room: RoomType, index: number) => (
							<Room
								key={'room' + room.room}
								data={room}
								cellWidth={cellWidth}
								first={index === 0}
							/>
						))}
					</View>
				</ScrollView>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		width: '100%',
		height: '100%',
	},
	action: {
		padding: 10,
		paddingBottom: 0,
		flexDirection: 'row',
	},
	date: {
		flex: 1,
	},
	btn: {
		marginTop: 20,
		height: 50,
		padding: 0,
	},
	btnText: {
		color: '#fff',
		fontSize: 25,
		lineHeight: 46,
		textAlign: 'center',
	},
	calendar: {
		flex: 1,
		margin: 10,
		backgroundColor: '#fff',
		borderRadius: 15,
		elevation: 5,
		borderColor: '#bdbdbd',
		borderWidth: 3,
		overflow: 'hidden',
	},
	scroll: {
		flexGrow: 0,
	},
	scrollInner: {
		height: '100%',
		minWidth: 1200,
	},
	header: {
		flexDirection: 'row',
		height: 50,
		marginLeft: 80,
		alignItems: 'center',
	},
	hour: {
		flex: 1,
	},
	hourLabel: {},
	loading: {
		marginTop: '15%',
	},
});

export default Timelines;
