{"rustc": 10895048813736897673, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5081136802505358982, "profile": 15657897354478470176, "path": 9186914441544841649, "deps": [[40386456601120721, "percent_encoding", false, 5447697539743764811], [1441306149310335789, "tempfile", false, 8689049205249851558], [3150220818285335163, "url", false, 15668146286072074414], [4899080583175475170, "semver", false, 985078205682465935], [5986029879202738730, "log", false, 14669300167346157817], [8218178811151724123, "reqwest", false, 9844371000314887433], [9010263965687315507, "http", false, 14314634951049504858], [9332307739160395223, "minisign_verify", false, 8519352421975377957], [9538054652646069845, "tokio", false, 17307833443289394789], [9689903380558560274, "serde", false, 10697150404045262645], [10281541584571964250, "windows_sys", false, 11368896552723085583], [10629569228670356391, "futures_util", false, 5778955098311341807], [10755362358622467486, "tauri", false, 17704364160439666055], [10806645703491011684, "thiserror", false, 11115062776651022174], [11721252211900136025, "build_script_build", false, 14281955196853153044], [12409575957772518135, "time", false, 10290409762142538993], [13077212702700853852, "base64", false, 10148683486352841222], [15367738274754116744, "serde_json", false, 12445351040836002554], [17146114186171651583, "infer", false, 16677533955296903182], [18372475104564266000, "zip", false, 5321652981952167125]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-updater-22fed5f941c4eeba\\dep-lib-tauri_plugin_updater", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}