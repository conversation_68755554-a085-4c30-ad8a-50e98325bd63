import { createSlice } from '@reduxjs/toolkit';

import { RootState } from '@/store';
import { fetchGifts, fetchProducts } from './AsyncThunks';
import { IGift } from '@/types/Cart';
import { IProduct, IProductGroup } from '@/types/Product';
import { parseCombo } from '@/utils/combo';

interface IProductsState {
	group: IProductGroup[];
	products: IProduct[];
	last: number;
	gifts: IGift[];
	combo: IProduct[];
}

const initialState: IProductsState = {
	group: [],
	products: [],
	last: 0,
	gifts: [],
	combo: [],
};

export const productsSlice = createSlice({
	name: 'products',
	initialState,
	reducers: {
		setGroup: (state, action) => {
			state.group = action.payload;
		},
		setProducts: (state, action) => {
			if (action.payload.length === 0) {
				state.combo.forEach((item) => {
					item.quantity = 0;
				});
			}
			state.products = action.payload;
		},
		addProduct: (state, action) => {
			let checkItem = state.products.findIndex(
				(item) => item.id === action.payload.item.id,
			);
			if (checkItem >= 0) {
				if (action.payload.type === 'increment') {
					++state.products[checkItem].quantity;
				} else {
					--state.products[checkItem].quantity;
					if (state.products[checkItem].quantity < 1) {
						state.products.splice(checkItem, 1);
					}
				}
			} else {
				state.products.push({
					...action.payload.item,
					quantity: 1,
				});
			}
		},
		addComboItem: (state, action) => {
			let checkItem = state.combo.findIndex(
				(item) => item.id === action.payload.item,
			);
			if (checkItem >= 0) {
				if (action.payload.type === 'increment') {
					++state.combo[checkItem].quantity;
				} else {
					--state.combo[checkItem].quantity;
					if (state.combo[checkItem].quantity < 1) {
						state.combo[checkItem].quantity = 0;
					}
				}
			}
		},
	},
	extraReducers: (builder) => {
		builder.addCase(fetchProducts.fulfilled, (state, action) => {
			state.group = action.payload;
			state.last = new Date().getTime();
			state.combo = parseCombo(state.group);
		});
		builder.addCase(fetchGifts.fulfilled, (state, action) => {
			state.gifts = action.payload;
		});
	},
});

export const { setGroup, setProducts, addProduct, addComboItem } =
	productsSlice.actions;

export const getLastFetch = (state: RootState) => state.products.last;

export default productsSlice.reducer;
