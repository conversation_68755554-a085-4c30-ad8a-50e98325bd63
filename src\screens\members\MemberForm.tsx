import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { MaskedTextInput } from 'react-native-mask-text';
import { useDebouncedCallback } from 'use-debounce';
import { router, useLocalSearchParams } from 'expo-router';

import Button from '@/components/Button';
import TextInput from '@/components/Input/TextInput';
import { update, create } from '@/services/Member';
import { showMessage } from '@/utils/Toast';
import { updateMember } from '@/store/Member/Slice';
import { isCustomer } from '@/utils';
import sendToCustomer, { channel } from '@/services/Broadcast';

import { IMemberForm } from '@/types/Member';
import { setLoading, setMemberId as setNewMemberId } from '@/store/App';
import { useAppDispatch } from '@/store';

export type MemberFormParams = {
	id?: string;
	member_id?: string;
	name?: string;
	phone?: string;
	email?: string;
	identification?: string;
	gender?: string;
	birthday?: string;
};

const MemberFormScreen = () => {
	const isCustomerScreen = isCustomer();

	const user = useLocalSearchParams<MemberFormParams>();
	const dispatch = useAppDispatch();

	const [memberId, setMemberId] = useState(user.member_id || '');
	const [name, setName] = useState(user.name || '');
	const [phone, setPhone] = useState(user.phone || '');
	const [email, setEmail] = useState(user.email || '');
	const [identification, setIdentification] = useState(
		user.identification || '',
	);
	const [gender, setGender] = useState(user.gender || '');
	const [birthday, setBirthday] = useState(user.birthday || '');

	const updateFormData = useDebouncedCallback((payload) => {
		sendToCustomer({
			action: 'memberForm',
			payload,
		});
	}, 800);

	channel.onmessage = (e: {
		data: { action: string; payload: IMemberForm };
	}) => {
		if (e?.data?.action && e.data.action === 'memberForm') {
			setMemberId(e.data.payload.memberId);
			setName(e.data.payload.name);
			setPhone(e.data.payload.phone);
			setEmail(e.data.payload.email);
			setIdentification(e.data.payload.identification);
			setGender(e.data.payload.gender);
			setBirthday(e.data.payload.birthday);
		}
	};

	useEffect(() => {
		updateFormData({
			memberId,
			name,
			phone,
			email,
			identification,
			gender,
			birthday,
		});
	}, [memberId, name, phone, email, identification, gender, birthday]); // eslint-disable-line react-hooks/exhaustive-deps

	const onSubmit = async () => {
		if (
			memberId.length === 0 ||
			name.length === 0 ||
			phone.length === 0 ||
			identification.length === 0
		) {
			showMessage({
				message: 'Vui lòng nhập đủ thông tin',
				type: 'danger',
			});
			return;
		}
		dispatch(setLoading(true));
		try {
			if (typeof user.id !== 'undefined') {
				const response = await update(user.id, {
					member_id: memberId,
					name,
					phone,
					email,
					gender,
					identification,
					birthday,
				});
				if (response.message) {
					showMessage({
						message: response.message,
					});
					dispatch(
						updateMember({
							id: user.id,
							data: {
								...user,
								member_id: memberId,
								name,
								phone,
								email,
								gender,
								identification,
								birthday,
							},
						}),
					);
				} else if (response.errors) {
					response.errors.forEach((err: string) =>
						showMessage({
							message: err,
							type: 'danger',
						}),
					);
				}
			} else {
				const response = await create({
					member_id: memberId,
					name,
					phone,
					email,
					gender,
					identification,
					birthday,
				});
				if (response.message) {
					showMessage({
						message: response.message,
					});
					dispatch(setNewMemberId(memberId));
					router.back();
				} else if (response.errors) {
					response.errors.forEach((err: string) =>
						showMessage({
							message: err,
							type: 'danger',
						}),
					);
				}
			}
		} catch (e) {
			console.log(e);
		}
		dispatch(setLoading(false));
	};

	return (
		<View style={styles.bg}>
			{isCustomerScreen && (
				<View style={styles.titleCustomer}>
					<Text style={styles.title}>Thông tin thành viên</Text>
				</View>
			)}
			<View style={styles.form}>
				<Text style={styles.label}>Mã khách hàng</Text>
				<TextInput
					value={memberId}
					onChangeText={setMemberId}
					style={styles.input}
					autoCapitalize="characters"
					clearButtonMode="always"
				/>
			</View>
			<View style={styles.form}>
				<Text style={styles.label}>Họ tên</Text>
				<TextInput
					value={name}
					onChangeText={setName}
					style={styles.input}
					clearButtonMode="always"
				/>
			</View>
			<View style={styles.form}>
				<Text style={styles.label}>Số điện thoại</Text>
				<TextInput
					value={phone}
					onChangeText={setPhone}
					style={styles.input}
					clearButtonMode="always"
				/>
			</View>
			<View style={styles.form}>
				<Text style={styles.label}>Email</Text>
				<TextInput
					value={email}
					onChangeText={setEmail}
					style={styles.input}
					clearButtonMode="always"
				/>
			</View>
			<View style={styles.form}>
				<Text style={styles.label}>CCCD/CMND</Text>
				<TextInput
					value={identification}
					onChangeText={setIdentification}
					style={styles.input}
					clearButtonMode="always"
				/>
			</View>
			<View style={styles.form}>
				<Text style={styles.label}>Ngày sinh</Text>
				{isCustomerScreen ? (
					<TextInput value={birthday} style={styles.input} />
				) : (
					<MaskedTextInput
						mask="99/99/9999"
						value={birthday}
						defaultValue={birthday}
						onChangeText={setBirthday}
						style={styles.input}
						placeholder="00/00/0000"
					/>
				)}
			</View>
			<View style={styles.form}>
				<Text style={styles.label}>Giới tính</Text>
				<Picker
					selectedValue={gender}
					onValueChange={setGender}
					style={styles.picker}>
					{['Nam', 'Nữ', 'Khác'].map((item, index) => (
						<Picker.Item label={item} value={item} key={index} />
					))}
				</Picker>
			</View>
			{!isCustomerScreen && (
				<View style={[styles.row, styles.center]}>
					<Button
						text="Quay lại"
						style={styles.btn}
						textStyle={styles.btnLabel}
						onPress={router.back}
						background="#129cf7"
					/>
					<Button
						onPress={onSubmit}
						text="Lưu"
						style={styles.btn}
						textStyle={styles.btnLabel}
					/>
				</View>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		padding: 15,
		backgroundColor: '#fff',
		borderRadius: 15,
		margin: 15,
		flex: 1,
	},
	row: {
		flexDirection: 'row',
	},
	label: {},
	input: {
		padding: 10,
		borderColor: '#ddd',
		borderWidth: 1,
	},
	form: {
		marginBottom: 5,
	},
	btn: {
		width: 120,
		padding: 10,
		margin: 20,
		alignItems: 'center',
	},
	btnLabel: {
		color: '#fff',
	},
	titleCustomer: {
		paddingVertical: 10,
	},
	title: {
		fontSize: 20,
		fontWeight: 'bold',
		textAlign: 'center',
	},
	picker: {
		padding: 10,
	},
	center: {
		justifyContent: 'center',
		alignItems: 'center',
	},
});

export default MemberFormScreen;
