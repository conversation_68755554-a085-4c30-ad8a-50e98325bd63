# Script to fix all remaining import paths

Write-Host "Fixing all remaining import paths..."

# List of files that need fixing based on ESLint output
$filesToFix = @(
    "src/components/CinemaScreen/index.tsx",
    "src/screens/exchange/List.tsx", 
    "src/screens/members/components/List.tsx",
    "src/screens/print/components/Tickets.tsx",
    "src/screens/products/components/Combo/Result.tsx",
    "src/screens/products/components/Combo/index.tsx",
    "src/screens/products/components/ProductGroup.tsx",
    "src/screens/products/components/SearchProduct.tsx",
    "src/screens/products/components/Sidebar/index.tsx",
    "src/screens/tickets/components/Showtimes.tsx",
    "src/screens/tickets/components/Tab.tsx",
    "src/screens/timelines/components/Tabs.tsx",
    "src/screens/timelines/index.tsx"
)

$totalFixed = 0

foreach ($filePath in $filesToFix) {
    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw -Encoding UTF8
        $originalContent = $content
        
        # Fix import paths
        $content = $content -replace '@/components/flatlist', '@/components/FlatList'
        $content = $content -replace '@/components/scrollview', '@/components/ScrollView'
        $content = $content -replace '@/components/image', '@/components/Image'
        $content = $content -replace '@/components/input', '@/components/Input'
        
        # Write back if changed
        if ($content -ne $originalContent) {
            Set-Content -Path $filePath -Value $content -Encoding UTF8 -NoNewline
            Write-Host "Fixed: $filePath"
            $totalFixed++
        }
    } else {
        Write-Host "File not found: $filePath"
    }
}

Write-Host "Fixed $totalFixed files."
Write-Host "All import paths should now be correct!"
