import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import primary from '@/constants/Colors';
import { router } from 'expo-router';
import Icon from '@expo/vector-icons/Ionicons';

type TopbarProps = {
	text: string;
	children?: React.ReactNode;
	goBack?: boolean | Function;
};

const Topbar = React.memo(({ text, children, goBack = false }: TopbarProps) => {
	const onPress = () => {
		if (typeof goBack === 'function') {
			goBack();
		} else {
			router.back();
		}
	};

	return (
		<View style={styles.bg}>
			{goBack && (
				<TouchableOpacity onPress={onPress} style={styles.back}>
					<Icon name="arrow-undo" size={30} color="#fff" />
					<Text style={styles.backTitle}>Quay lại</Text>
				</TouchableOpacity>
			)}
			<View style={styles.header}>
				<Text numberOfLines={1} style={styles.title}>
					{text}
				</Text>
				{children}
			</View>
		</View>
	);
});

Topbar.displayName = 'Topbar';

const styles = StyleSheet.create({
	bg: {
		backgroundColor: primary,
		paddingLeft: 15,
		height: 60,
		paddingRight: 250,
		alignItems: 'center',
		flexDirection: 'row',
	},
	back: {
		alignItems: 'center',
		flexDirection: 'row',
	},
	backTitle: {
		color: '#fff',
		fontSize: 16,
		paddingLeft: 5,
	},
	header: {
		flex: 1,
		alignItems: 'center',
	},
	title: {
		color: '#fff',
		fontSize: 20,
		fontWeight: 'bold',
		textAlign: 'center',
	},
	bold: {
		fontWeight: 'bold',
	},
});

export default Topbar;
