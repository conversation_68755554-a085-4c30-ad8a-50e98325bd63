import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import Icon from '@expo/vector-icons/Ionicons';

import FlatList, { FlatListRef } from '@/components/FlatList';
import sendToCustomer, { channel } from '@/services/Broadcast';
import primary from '@/constants/Colors';
import Movie from './Movie';
import { IMovie, IShowtime } from '../types';

type TabProps = {
	data: {
		movies: IMovie[];
	};
	index: string;
};

const Tab = React.memo(({ data, index }: TabProps) => {
	const list = useRef<FlatListRef<IMovie>>(null);
	const renderItem = ({ item }: { item: IMovie }) => <Movie data={item} />;

	const [canDown, setDown] = useState(true);

	useEffect(() => {
		channel.addEventListener('message', (e: any) => {
			if (typeof e.data.scrollerPos !== 'undefined') {
				handleScroll(e.data.scrollerPos);
			}
		});

		return () => {
			channel.removeEventListener('message', (e: any) => {
				console.log(e);
			});
		};
	}, []);

	const toTop = () => {
		handleScroll(true);
		sendToCustomer({
			scrollerPos: true,
		});
	};

	const toEnd = () => {
		handleScroll(false);
		sendToCustomer({
			scrollerPos: false,
		});
	};

	const handleScroll = (top: boolean) => {
		if (list.current) {
			if (top) {
				list.current.scrollToOffset({
					offset: 0,
				});
				setDown(true);
			} else {
				list.current.scrollToEnd();
				setDown(false);
			}
		}
	};

	const movies = data.movies.filter((movie: IMovie) =>
		movie.showtime.some((showtime: IShowtime) => showtime.status),
	);

	return (
		<View style={styles.wrap}>
			<View style={styles.list}>
				<FlatList
					ref={list}
					data={movies}
					renderItem={renderItem}
					keyExtractor={(item) => item.id + '-' + index}
					numColumns={2}
					style={styles.flatlist}
				/>
			</View>
			{movies.length > 6 && (
				<View style={styles.pagination}>
					<TouchableOpacity onPress={toTop} style={styles.arrow}>
						<Icon
							name="arrow-up-outline"
							size={40}
							color={canDown ? '#a7a4a4' : primary}
						/>
					</TouchableOpacity>
					<TouchableOpacity onPress={toEnd} style={styles.arrow}>
						<Icon
							name="arrow-down-outline"
							size={40}
							color={canDown ? primary : '#a7a4a4'}
						/>
					</TouchableOpacity>
				</View>
			)}
		</View>
	);
});

Tab.displayName = 'Tab';

const styles = StyleSheet.create({
	wrap: {
		flexDirection: 'column',
		flex: 1,
	},
	list: {
		flex: 1,
		flexDirection: 'row',
		paddingHorizontal: 5,
	},
	flatlist: {},
	pagination: {
		flexDirection: 'row',
		justifyContent: 'flex-end',
	},
	arrow: {
		width: 46,
		height: 46,
		backgroundColor: '#ddd',
		borderColor: '#a7a4a4',
		borderWidth: 1,
		margin: 5,
		borderRadius: 46,
		alignContent: 'center',
		justifyContent: 'center',
		alignItems: 'center',
	},
	content: {
		flexDirection: 'column',
		flexWrap: 'wrap',
		alignItems: 'center',
		justifyContent: 'center',
		alignContent: 'center',
	},
	bgImage: {
		width: '100%',
		height: '100%',
	},
	overlay: {
		position: 'absolute',
		width: '100%',
		height: '100%',
		backgroundColor: 'rgba(0, 0, 0, .5)',
	},
	modal: {
		justifyContent: 'flex-end',
		margin: 0,
	},
	showtimes: {
		position: 'absolute',
		bottom: 0,
		width: 1040,
		marginBottom: -100,
		alignSelf: 'center',
	},
});

export default Tab;
