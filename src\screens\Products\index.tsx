import React, { useEffect, useState } from 'react';
import { View, StyleSheet, InteractionManager } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

import List from './List';
import Sidebar from './components/Sidebar';
import { AppDispatch } from '@/store';
import { fetchProducts, getLastFetch } from '@/store/product';

const ProductsScreen = () => {
	const dispatch = useDispatch<AppDispatch>();
	const lastFetch = useSelector(getLastFetch);

	const [isReady, setIsReady] = useState<boolean>(false);

	useEffect(() => {
		if (new Date().getTime() - lastFetch > 10 * 60 * 1000) {
			dispatch(fetchProducts(false));
		}
		const interactionPromise = InteractionManager.runAfterInteractions(() => {
			setIsReady(true);
		});
		return () => {
			interactionPromise.cancel();
		};
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	return (
		<View style={styles.bg}>
			<View style={styles.leftColumn}>
				<View style={styles.container}>
					<List ready={isReady} />
				</View>
			</View>
			<View style={styles.rightColumn}>
				<Sidebar />
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
	},
	leftColumn: {
		flex: 1,
	},
	container: {
		flex: 1,
		backgroundColor: 'rgba(255, 255, 255, .2)',
		overflow: 'hidden',
	},
	rightColumn: {
		width: 360,
		backgroundColor: '#fff',
		boxShadow: '0 0 10px rgba(0, 0, 0, .3)',
	},
	icon: {
		zIndex: 9,
		width: 453,
		height: 180,
		marginLeft: 40,
		marginTop: 20,
	},
});

export default ProductsScreen;
