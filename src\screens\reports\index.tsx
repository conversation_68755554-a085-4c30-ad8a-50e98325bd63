import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, TextInput, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Picker } from '@react-native-picker/picker';

import { get as getAPI, post as postAPI } from '@/services';
import { currencyFormat, getDate } from '@/utils';
import Button from '@/components/Button';
import Separator from '@/components/Separator';
import Detail from './components/Detail';
import Results from './components/Results';
import { IReport, IReportShift } from '@/types/Report';
import { getConfigs, getUser, setLoading } from '@/store/App';
import printTask, {
	IReport as IPrintReport,
	IProduct as IPrintProduct,
} from '@/utils/printer';
import primary from '@/constants/Colors';

const ReportsScreen = () => {
	const dispatch = useDispatch();

	const [shifts, setShifts] = useState<IReportShift[]>([]);
	const [from, setFrom] = useState<string>(getDate() + ' 06:00');
	const [to, setTo] = useState<string>(getDate() + ' 23:59');
	const [shift, setShift] = useState(3);
	const [data, setData] = useState<IReport>();

	const user = useSelector(getUser);
	const configs = useSelector(getConfigs);

	useEffect(() => {
		const getShifts = async () => {
			try {
				const response = await getAPI('shifts');
				setShifts(response.data);
			} catch (e) {
				console.log(e);
			}
		};
		getShifts();
	}, []);

	const onReport = async () => {
		dispatch(setLoading(true));
		try {
			const response = await postAPI('report', {
				from,
				to,
				shift,
			});
			setData({
				detail: {
					start: response.data.from,
					end: response.data.to,
					user: response.data.fullname,
					amount: response.data.amount,
					shift: response.data.shift,
				},
				tickets: response.data.tickets,
				products: response.data['foods-drinks'],
				overview: response.data.products,
				categories: response.data.categories,
				total_categories: response.data.total_categories,
			});
		} catch (e) {
			console.log(e);
		}
		dispatch(setLoading(false));
	};

	const onPrint = () => {
		if (data) {
			const report: IPrintReport = {
				time: data.detail.start + ' - ' + data.detail.end,
				counter: configs.counter,
				seller: data.detail.user,
				shift: data.detail.shift,
				total: currencyFormat(data.detail.amount.amount_total) + ' VNĐ',
				sell: currencyFormat(data.detail.amount.amount_sell) + ' VNĐ',
				cancel: currencyFormat(data.detail.amount.amount_total_cancel) + ' VNĐ',
				cancel_count: data.tickets.canceled ? data.tickets.canceled.length : 0,
				ticket: currencyFormat(data.detail.amount.amount_ticket) + ' VNĐ',
				ticket_count: data.tickets.success ? data.tickets.success.length : 0,
				combo: currencyFormat(data.detail.amount.amount_combo) + ' VNĐ',
				combo_count: data.products.success ? data.products.success.length : 0,
				cash: currencyFormat(data.detail.amount.amount_cash) + ' VNĐ',
				card: currencyFormat(data.detail.amount.card) + ' VNĐ',
				momo: currencyFormat(data.detail.amount.momo) + ' VNĐ',
				vnpay: currencyFormat(data.detail.amount.vnpay) + ' VNĐ',
				shopeepay: currencyFormat(data.detail.amount.shopeepay || 0) + ' VNĐ',
				grab: currencyFormat(data.detail.amount.grab || 0) + ' VNĐ',
				beamin: currencyFormat(data.detail.amount.beamin || 0) + ' VNĐ',
				voucher: currencyFormat(data.detail.amount.amount_voucher) + ' VNĐ',
				voucher_count: data.detail.amount.voucher_count,
			};
			printTask(report, configs.printer, 'report');
		}
	};

	const onPrintProducts = () => {
		if (data) {
			const products: IPrintProduct = {
				time: data.detail.start + ' - ' + data.detail.end,
				counter: configs.counter,
				seller: data.detail.user,
				shift: data.detail.shift,
				products: data.categories.map((item) => {
					return [item.name, item.total];
				}),
				product_total: data.total_categories,
			};
			printTask(products, configs.printer, 'product');
		}
	};

	const canPrintProducts: boolean = data
		? data.categories && data.categories.length > 0
		: false;

	return (
		<View style={styles.bg}>
			<View style={styles.colDetail}>
				<View style={styles.detail}>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text>Từ</Text>
						</View>
						<View style={styles.value}>
							<TextInput
								value={from}
								onChangeText={setFrom}
								style={styles.input}
							/>
						</View>
					</View>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text>Đến</Text>
						</View>
						<View style={styles.value}>
							<TextInput value={to} onChangeText={setTo} style={styles.input} />
						</View>
					</View>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text>Nhân viên</Text>
						</View>
						<View style={styles.value}>
							<TextInput value={user.name} style={styles.input} />
						</View>
					</View>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text>Ca</Text>
						</View>
						<View style={styles.value}>
							<Picker
								selectedValue={shift}
								onValueChange={setShift}
								style={styles.picker}>
								{shifts.map((item) => (
									<Picker.Item
										label={item.name}
										value={item.id}
										key={item.id}
									/>
								))}
							</Picker>
						</View>
					</View>
					<Separator />
					{data && (
						<Detail
							detail={data.detail}
							total={{
								tickets: data.tickets?.success
									? data.tickets?.success.length
									: 0,
								products: data.products?.success
									? data.products?.success.length
									: 0,
							}}
						/>
					)}
				</View>
				<View style={styles.action}>
					<Button
						onPress={onReport}
						text="Thống kê"
						style={styles.btn}
						textStyle={styles.btnLabel}
					/>
					{data && (
						<>
							<Button
								onPress={onPrint}
								text="In kết ca"
								style={styles.btn}
								textStyle={styles.btnLabel}
								background={
									typeof data.detail === 'undefined' ? '#ddd' : primary
								}
								disabled={typeof data.detail === 'undefined'}
							/>
							<Button
								onPress={onPrintProducts}
								text="In hàng hóa"
								style={styles.btn}
								textStyle={styles.btnLabel}
								background={!canPrintProducts ? '#ddd' : primary}
								disabled={!canPrintProducts}
							/>
						</>
					)}
				</View>
			</View>
			{data && (
				<Results
					overview={data.overview}
					categories={data.categories}
					total={data.total_categories || 0}
				/>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
		paddingHorizontal: 20,
		paddingVertical: 10,
	},
	colDetail: {
		width: 350,
		backgroundColor: '#fff',
		padding: 10,
	},
	detail: {
		flex: 1,
	},
	action: {},
	row: {
		flexDirection: 'row',
		marginVertical: 5,
		alignItems: 'center',
	},
	label: {
		width: 80,
	},
	value: {
		flex: 1,
	},
	picker: {},
	btn: {
		marginVertical: 5,
	},
	btnLabel: {
		textAlign: 'center',
		color: '#fff',
	},
	input: {},
});

export default ReportsScreen;
