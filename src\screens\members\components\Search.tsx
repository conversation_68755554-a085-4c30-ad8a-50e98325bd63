import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import Icon from '@expo/vector-icons/Ionicons';
import { router } from 'expo-router';

import Button from '@/components/Button';
import TextInput from '@/components/Input/TextInput';
import { fectchData, SearchMemberData } from '@/store/Member';
import { useAppDispatch } from '@/store';
import { getMember } from '@/store/Cart';

const Search = React.memo(() => {
	const dispatch = useAppDispatch();

	const member = useSelector(getMember);

	const [memberId, setMemberId] = useState('');
	const [name, setName] = useState('');
	const [phone, setPhone] = useState('');
	const [identification, setIdentification] = useState('');

	useEffect(() => {
		if (member) {
			onSearch();
		}
	}, [member]); // eslint-disable-line react-hooks/exhaustive-deps

	const onSearch = () => {
		const params: SearchMemberData = {
			id: memberId,
			name,
			phone,
			identification,
		};
		dispatch(fectchData(params));
	};

	const createMember = () => {
		router.navigate('/pos/members/create');
	};

	return (
		<View style={styles.bg}>
			<View style={styles.row}>
				<View style={styles.col}>
					<TextInput
						value={memberId}
						onChangeText={setMemberId}
						style={styles.input}
						placeholder="Mã khách hàng"
						autoCapitalize="characters"
						onSubmitEditing={onSearch}
						clearButtonMode="always"
					/>
				</View>
				<View style={styles.col}>
					<TextInput
						value={name}
						onChangeText={setName}
						style={styles.input}
						placeholder="Họ tên"
						onSubmitEditing={onSearch}
						clearButtonMode="always"
					/>
				</View>
				<View style={styles.col}>
					<TextInput
						value={phone}
						onChangeText={setPhone}
						style={styles.input}
						placeholder="SĐT"
						onSubmitEditing={onSearch}
						clearButtonMode="always"
					/>
				</View>
				<View style={styles.col}>
					<TextInput
						value={identification}
						onChangeText={setIdentification}
						style={styles.input}
						placeholder="CMND/CCCD"
						onSubmitEditing={onSearch}
						clearButtonMode="always"
					/>
				</View>
			</View>
			<View style={styles.row}>
				<Button onPress={onSearch} style={styles.btn}>
					<Icon name="search-outline" size={25} color="#fff" />
					<Text style={styles.btnLabel}>Tìm</Text>
				</Button>
				<View style={styles.right}>
					<Button onPress={createMember} style={styles.btn}>
						<Icon name="add-outline" size={25} color="#fff" />
						<Text style={styles.btnLabel}>Thêm KH</Text>
					</Button>
				</View>
			</View>
		</View>
	);
});

Search.displayName = 'SearchMember';

const styles = StyleSheet.create({
	bg: {},
	row: {
		flexDirection: 'row',
	},
	col: {
		flex: 1,
		margin: 5,
	},
	input: {
		padding: 10,
		borderColor: '#ddd',
		borderWidth: 1,
	},
	btn: {
		width: 150,
		marginVertical: 5,
		padding: 8,
		flexDirection: 'row',
		alignContent: 'center',
		justifyContent: 'center',
		alignItems: 'center',
	},
	btnLabel: {
		color: '#fff',
		fontSize: 18,
		paddingLeft: 10,
	},
	right: {
		flex: 1,
		alignItems: 'flex-end',
	},
});

export default Search;
