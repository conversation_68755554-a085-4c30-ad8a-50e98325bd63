﻿import React, { useMemo, useState } from 'react';
import { StyleSheet, View, Image, ActivityIndicator, Text } from 'react-native';
import { useSelector } from 'react-redux';
import isEqual from 'react-fast-compare';

import Modal from '@/components/modal';
import Button from '@/components/button';

import { useAppDispatch } from '@/store';
import { getQrcode, setQrcode } from '@/store/cart';
import { queryQrOrder } from '@/services/Kiosk';
import { showMessage } from '@/utils/Toast';
import { currencyFormat, isCustomer } from '@/utils';
import primary from '@/constants/Colors';

type Props = {
	orderId: number;
	amount: number;
	onSuccess(check?: boolean): void;
	payment: string;
};

const Qrcode = React.memo(({ orderId, onSuccess, amount, payment }: Props) => {
	const dispatch = useAppDispatch();

	const qr = useSelector(getQrcode);

	const [isLoading, setLoading] = useState<boolean>(false);

	const closeModal = () => {
		dispatch(setQrcode(null));
	};

	const visible = useMemo(() => {
		return typeof qr === 'string' && qr.length > 0;
	}, [qr]);

	const onChecking = async () => {
		setLoading(true);
		try {
			const response = await queryQrOrder(orderId, payment, amount);
			setLoading(false);
			if (response && response.amount === amount) {
				closeModal();
				showMessage({
					message: 'Thanh toán ' + payment + ' thành công',
				});
				setTimeout(() => {
					requestAnimationFrame(() => {
						onSuccess(false);
					});
				}, 500);
			}
		} catch (e) {
			console.log(e);
			setLoading(false);
		}
	};

	return (
		<>
			<Modal visible={visible} onRequestClose={closeModal} style={styles.modal}>
				<View style={styles.modalBody}>
					{typeof qr === 'string' && (
						<Image source={{ uri: qr }} style={styles.qrcode} />
					)}
					{!isCustomer() ? (
						<>
							<Button
								onPress={onChecking}
								style={styles.submit}
								text={isLoading ? ' Kiểm tra...' : '  Kiểm tra  '}
								disabled={isLoading}
							/>
							<View style={{ opacity: isLoading ? 1 : 0 }}>
								<ActivityIndicator size="large" color={primary} />
							</View>
						</>
					) : (
						<>
							<Text style={styles.text}>
								Số tiền thanh toán:{' '}
								<Text style={styles.price}>{currencyFormat(amount)}</Text>
							</Text>
						</>
					)}
				</View>
			</Modal>
		</>
	);
}, isEqual);

Qrcode.displayName = 'Qrcode';

const styles = StyleSheet.create({
	qrcode: {
		width: 300,
		height: 300,
	},
	modal: {},
	modalBody: {
		backgroundColor: '#fff',
		padding: 40,
		width: 380,
		paddingBottom: 10,
		alignItems: 'center',
		position: 'relative',
	},
	submit: {
		marginTop: 20,
		marginBottom: 10,
		width: 140,
		alignItems: 'center',
	},
	text: {
		fontSize: 16,
		padding: 10,
	},
	price: {
		fontSize: 18,
		fontWeight: 'bold',
		color: primary,
	},
});

export default Qrcode;
