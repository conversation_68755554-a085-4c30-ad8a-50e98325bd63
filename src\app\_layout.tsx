﻿import React, { useEffect } from 'react';
import { Stack } from 'expo-router';
import { Provider, useSelector } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

import store, { useAppDispatch } from '@/store';
import Loading from '@/components/loading';
import { getConfigs, getUserData, setConfigs } from '@/store/app';
import { initMonitor } from '@/modules/pos/monitor';

export default function RootLayout() {
	return (
		<Provider store={store}>
			<AppContainer />
		</Provider>
	);
}

const AppContainer = () => {
	const configs = useSelector(getConfigs);

	const dispatch = useAppDispatch();

	useEffect(() => {
		initMonitor();
		init();
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	const init = async () => {
		const raw = await AsyncStorage.getItem('configs');
		if (raw != null) {
			const data = JSON.parse(raw);
			dispatch(setConfigs(data));
			if (data.server) {
				const token = await AsyncStorage.getItem('token');
				if (token) {
					dispatch(getUserData(token));
				}
			}
		} else {
			dispatch(setConfigs(configs));
		}
	};

	return (
		<>
			<Stack
				screenOptions={{
					headerShown: false,
				}}
			/>
			<Loading />
		</>
	);
};
