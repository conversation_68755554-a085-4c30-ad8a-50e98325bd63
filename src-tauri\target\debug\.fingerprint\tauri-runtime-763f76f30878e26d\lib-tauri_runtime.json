{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 15136111467907693914, "deps": [[442785307232013896, "build_script_build", false, 465213733085819490], [3150220818285335163, "url", false, 15668146286072074414], [4143744114649553716, "raw_window_handle", false, 10079824973013050502], [7606335748176206944, "dpi", false, 18280051305849568070], [9010263965687315507, "http", false, 14314634951049504858], [9689903380558560274, "serde", false, 10697150404045262645], [10806645703491011684, "thiserror", false, 11115062776651022174], [11050281405049894993, "tauri_utils", false, 3638413504225201697], [14585479307175734061, "windows", false, 15772416081231344459], [15367738274754116744, "serde_json", false, 12445351040836002554], [16727543399706004146, "cookie", false, 17761899994528077906]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-763f76f30878e26d\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}