﻿import React, { useState } from 'react';
import {
	ActivityIndicator,
	StyleSheet,
	Text,
	TextStyle,
	TouchableOpacity,
	View,
	ViewStyle,
} from 'react-native';
import { useSelector } from 'react-redux';
import isEqual from 'react-fast-compare';

import { showMessage } from '@/utils/Toast';
import Button from '@/components/button';
import Modal from '@/components/modal';
import { getPrices, getSelected } from '@/store/booking/Selector';
import { holdSeats } from '@/services/Booking';
import { currentShowtime, setSeats } from '@/store/booking/Slice';
import TextInput from '@/components/input/TextInput';
import { RootState, useAppDispatch } from '@/store';
import { IBookingSeat } from '@/types/Booking';
import { ICartTicket } from '@/types/Cart';
import { getConfigs, getUser, IConfig } from '@/store/app';
import printTask from '@/utils/printer';
import primary from '@/constants/Colors';

type Props = {
	style?: ViewStyle;
	textStyle?: TextStyle;
};

const HoldButton = React.memo(({ style, textStyle }: Props) => {
	const dispatch = useAppDispatch();

	const user = useSelector(getUser);
	const showtime = useSelector(currentShowtime);
	const prices = useSelector(getPrices);

	const [modal, setModal] = useState(false);
	const [isLoading, setLoading] = useState(false);
	const [reason, setReason] = useState('');
	const configs = useSelector<RootState, IConfig>(getConfigs);

	const seats = useSelector(getSelected);

	const openModal = () => {
		setModal(true);
	};

	const closeModal = () => {
		setModal(false);
	};

	const doHold = async (print = false) => {
		setLoading(true);
		try {
			const response = await holdSeats({
				seats: seats.map((seat: IBookingSeat) => {
					return {
						seat_id: seat.id,
						price_id: seat.price_id,
					};
				}),
				reason,
				print,
			});
			if (print) {
				doPrint(response.data, seats);
			}
			dispatch(setSeats([]));
			setTimeout(() => {
				showMessage({
					message: 'Giữ ghế thành công',
					type: 'success',
				});
			}, 500);
			setModal(false);
		} catch (e) {
			console.log(e);
		}
		setLoading(false);
	};

	const doPrint = async (tickets: ICartTicket[], seats: IBookingSeat[]) => {
		if (tickets.length > 0) {
			await printTask(
				{
					tickets: tickets.map((item) => {
						const seat = seats.find((seat) => seat.id === item.seat_id);
						const price = prices.find((price) => price.id === seat?.price_id);
						return {
							...item,
							movie_age: showtime?.age,
							price: price?.price || 0,
							target: price?.price_for_name || 'Người lớn',
						};
					}),
					seller: user.name,
				},
				configs.printer,
				'ticket',
			);
		}
	};

	const onHold = () => {
		doHold();
	};

	const onPrint = () => {
		doHold(true);
	};

	if (user?.permission?.indexOf('hold_chair') < 0) {
		return null;
	}

	return (
		<View style={[styles.btn, style]}>
			<TouchableOpacity onPress={openModal} style={styles.btnWrap}>
				<Text style={textStyle}>Giữ ghế</Text>
			</TouchableOpacity>
			<Modal visible={modal} onRequestClose={closeModal} style={styles.modal}>
				<View style={styles.modalBody}>
					<View style={styles.inputWrap}>
						{modal && (
							<TextInput
								value={reason}
								onChangeText={setReason}
								placeholder="Ghi chú"
								autoFocus
								style={styles.input}
								clearButtonMode="always"
							/>
						)}
					</View>
					<View style={styles.action}>
						<Button onPress={onHold} text="Giữ ghế" style={styles.submit} />
						<Button
							onPress={onPrint}
							text="Giữ ghế + In vé"
							style={styles.submit}
						/>
					</View>
					{isLoading && (
						<View style={styles.loading}>
							<View style={styles.loader}>
								<ActivityIndicator size="large" color={primary} />
							</View>
						</View>
					)}
				</View>
			</Modal>
		</View>
	);
}, isEqual);

HoldButton.displayName = 'HoldButton';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	loading: {
		position: 'absolute',
		top: 0,
		left: 0,
		width: 380,
		height: 200,
		zIndex: 999,
		backgroundColor: 'rgba(0, 0, 0 ,.5)',
	},
	loader: {
		alignItems: 'center',
		flex: 1,
		justifyContent: 'center',
		top: '40%',
		position: 'absolute',
		width: '100%',
	},
	modal: {},
	inputWrap: {
		paddingVertical: 20,
	},
	btn: {
		paddingHorizontal: 20,
		marginHorizontal: 10,
		borderRadius: 6,
		backgroundColor: primary,
		flex: 1,
	},
	btnWrap: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: primary,
	},
	modalBody: {
		backgroundColor: '#fff',
		padding: 40,
		width: 380,
		height: 200,
		alignItems: 'center',
	},
	input: {
		padding: 10,
		width: 300,
		borderWidth: 1,
		borderColor: '#ddd',
		borderRadius: 5,
	},
	action: {
		flexDirection: 'row',
	},
	submit: {
		alignItems: 'center',
	},
});

export default HoldButton;
