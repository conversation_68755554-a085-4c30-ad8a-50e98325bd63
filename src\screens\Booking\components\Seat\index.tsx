import React from 'react';
import { StyleSheet, Text, Pressable, View } from 'react-native';
import { useSelector } from 'react-redux';
import { showMessage } from '@/utils/Toast';
import isEqual from 'react-fast-compare';

import { seat as colors } from '@/constants/Colors';
import { addSeat, currentShowtime, removeSeat } from '@/store/Booking/Slice';
import { getSeat } from '@/store/Booking/Selector';
import { post } from '@/services';
import { useAppDispatch } from '@/store';

const Seat = React.memo(
	({ id, isSelected }: { id: number; isSelected: boolean }) => {
		const dispatch = useAppDispatch();
		const showtime = useSelector(currentShowtime);

		const seat = useSelector(getSeat(id));
		if (!seat) {
			return null;
		}

		if (seat.type === 'none') {
			return <View style={styles.seat} />;
		}

		var color = seat.color;

		if (seat.status === 'booking') {
			color = colors.booking;
		}

		if (seat.status === 'selected' || isSelected) {
			color = colors.selected;
		}

		if (seat.status === 'booked') {
			color = colors.booked;
		}

		if (seat.status === 'booked' && seat.fa) {
			color = colors.fa;
		}

		var isDisabled = false;

		if (typeof seat.disable !== 'undefined' && seat.disable === true) {
			isDisabled = true;
		}

		const onPress = async () => {
			if (isDisabled) {
				return;
			}
			if (seat.status !== 'booked') {
				if (seat.status === 'booking' && !isSelected) {
					showMessage({
						message: 'Không thể chọn ghế ' + seat.name,
						type: 'danger',
					});
					return;
				}
				if (isSelected) {
					dispatch(removeSeat(seat.id));
					try {
						await post('/cinema/unselect', { seat_id: seat.id });
					} catch (e) {
						console.log(e);
					}
				} else if (showtime) {
					dispatch(
						addSeat({
							id: seat.id,
							name: seat.name,
							type_seat_id: seat.type_seat_id,
							type_seat_name: seat.type_seat_name,
							showtime_id: showtime.id,
						}),
					);
					try {
						await post('/cinema/select', { seat_id: seat.id });
					} catch (e) {
						dispatch(removeSeat(seat.id));
						console.log(e);
					}
				}
			}
		};

		const size = {
			width: parseInt(seat.w),
			height: parseInt(seat.h),
			top: parseInt(seat.y),
			left: parseInt(seat.x),
		};

		return (
			<Pressable onPress={onPress} style={[styles.seat, size]}>
				<View
					style={[
						styles.seatWrap,
						{
							backgroundColor: color,
							height: size.height / 2,
							top: size.height / 2,
						},
					]}></View>
				<View
					style={[
						styles.seatInner,
						{ backgroundColor: color, height: (size.height / 5) * 4 },
					]}>
					<Text
						style={[
							styles.label,
							seat.status === 'booking' ? { color: '#000' } : null,
						]}>
						{seat.name}
					</Text>
				</View>
			</Pressable>
		);
	},
	isEqual,
);

Seat.displayName = 'Seat';

const styles = StyleSheet.create({
	seat: {
		position: 'absolute',
	},
	seatWrap: {
		width: '100%',
		borderRadius: 10,
		borderWidth: 1,
		borderColor: '#000',
	},
	seatInner: {
		position: 'absolute',
		width: '80%',
		left: '10%',
		top: '10%',
		borderRadius: 15,
		borderWidth: 1,
		borderColor: '#000',
		justifyContent: 'center',
	},
	label: {
		color: '#fff',
		fontWeight: 'bold',
		textAlign: 'center',
		fontSize: 20,
	},
	labelBooked: {
		color: '#000',
	},
});

export default Seat;
