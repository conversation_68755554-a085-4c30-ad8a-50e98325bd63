import React, { memo, useMemo, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';

import Item from './ProductItem';
import { getGroup } from '@/store/Product/Selector';
import TextInput from '@/components/Input/TextInput';
import { IProduct } from '../types';
import FlatList from '@/components/FlatList';

const SearchProduct = memo(() => {
	const [search, setSearch] = useState('');
	const products = useSelector(getGroup);

	const filterProducts = useMemo(() => {
		if (search.length > 0) {
			const items = [];
			const key = search.replace('+', '\\+').replace('[', '').replace(']', '');
			for (const gr of products) {
				for (const item of gr.data) {
					if (item.name.match(new RegExp(key, 'ig'))) {
						items.push(item);
					}
				}
			}
			return items;
		}
		return [];
	}, [search, products]);

	const renderItem = ({ item }: { item: IProduct }) => <Item data={item} />;

	return (
		<View style={[styles.list, search.length > 0 ? styles.full : null]}>
			<View style={styles.search}>
				<TextInput
					value={search}
					onChangeText={setSearch}
					placeholder="Tìm nhanh..."
					style={styles.input}
					clearButtonMode="always"
				/>
			</View>
			{search.length > 0 && (
				<FlatList
					keyExtractor={(item) => item.id}
					renderItem={renderItem}
					data={filterProducts}
					numColumns={4}
					persistentScrollbar={true}
					ListEmptyComponent={
						<View style={styles.empty}>
							<Text style={styles.emptyText}>
								Không tìm thấy sản phẩm phù hợp
							</Text>
						</View>
					}
				/>
			)}
		</View>
	);
});

SearchProduct.displayName = 'ProductGroup';

const styles = StyleSheet.create({
	list: {
		marginBottom: 10,
	},
	full: {
		height: '100%',
	},
	search: {
		backgroundColor: '#fff',
		paddingHorizontal: 30,
		paddingVertical: 10,
	},
	input: {
		padding: 10,
		backgroundColor: '#eee',
		borderRadius: 5,
		borderBottomWidth: 0,
	},
	empty: {
		padding: 20,
		alignContent: 'center',
		alignItems: 'center',
	},
	emptyText: {
		fontSize: 16,
		fontWeight: 'bold',
	},
});

export default SearchProduct;
