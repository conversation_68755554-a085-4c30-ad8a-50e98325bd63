{"rustc": 10895048813736897673, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 15748660880806768043, "deps": [[4899080583175475170, "semver", false, 2426471561692137791], [6913375703034175521, "schemars", false, 17719869593701131168], [7170110829644101142, "json_patch", false, 3739604338288812391], [9689903380558560274, "serde", false, 17710615169291184661], [11050281405049894993, "tauri_utils", false, 8694799301641733153], [12714016054753183456, "tauri_winres", false, 14247200909230236972], [13077543566650298139, "heck", false, 290801593150339784], [13475171727366188400, "cargo_toml", false, 8725196036364720916], [13625485746686963219, "anyhow", false, 12400837003654224066], [15367738274754116744, "serde_json", false, 4006064592906587583], [15609422047640926750, "toml", false, 980020049899024968], [15622660310229662834, "walkdir", false, 11897401627305349443], [16928111194414003569, "dirs", false, 9511758020444443471], [17155886227862585100, "glob", false, 10599412529121352411]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-82cb30a5153882cb\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}