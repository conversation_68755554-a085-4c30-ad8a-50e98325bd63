import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';

import Sidebar from './components/Sidebar';
import List from './List';
import primary from '@/constants/Colors';

const ExchangeScreen = () => {
	const [ready, setReady] = useState(false);

	useEffect(() => {
		requestAnimationFrame(() => {
			setReady(true);
		});
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	return (
		<View style={styles.bg}>
			<View style={styles.colLeft}>
				<View>
					<View style={styles.header}>
						<Text style={styles.headerText}>Đ<PERSON>i điểm</Text>
					</View>
				</View>
				<View style={styles.container}>
					<List ready={ready} />
				</View>
			</View>
			<View style={styles.colRight}>
				<Sidebar />
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
	},
	header: {
		backgroundColor: primary,
		alignItems: 'center',
		padding: 15,
	},
	headerText: {
		color: '#fff',
		fontSize: 16,
	},
	colLeft: {
		flex: 1,
	},
	container: {
		flex: 1,
		backgroundColor: 'rgba(255, 255, 255, .2)',
		overflow: 'hidden',
	},
	colRight: {
		width: 360,
		backgroundColor: '#fff',
	},
	icon: {
		zIndex: 9,
		width: 453,
		height: 180,
		marginLeft: 40,
		marginTop: 20,
	},
});

export default ExchangeScreen;
