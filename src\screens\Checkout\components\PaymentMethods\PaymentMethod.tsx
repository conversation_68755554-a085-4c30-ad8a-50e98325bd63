import primary from '@/constants/Colors';
import { useAppDispatch } from '@/store';
import { setPaymentMethod } from '@/store/cart';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export const Icons: Record<string, any> = {
	'0': require('@/assets/images/icons/money.png'),
	'1': require('@/assets/images/icons/atm.png'),
	momo: require('@/assets/images/icons/momo.png'),
	vnpay: require('@/assets/images/icons/vnpay.png'),
	shopeepay: require('@/assets/images/icons/shopeepay.png'),
	grab: require('@/assets/images/icons/grab.png'),
};

type Props = {
	data: {
		id: string;
		name: string;
	};
	active: string;
};
const PaymentMethod = ({ data, active }: Props) => {
	const dispatch = useAppDispatch();
	const onPress = () => {
		dispatch(setPaymentMethod(data.id));
	};
	const Icon =
		typeof Icons[data.id] !== 'undefined' ? Icons[data.id] : Icons[0];
	return (
		<TouchableOpacity style={styles.btn} onPress={onPress}>
			<View style={[styles.payment, data.id === active ? styles.active : {}]}>
				<Image source={Icon} style={[styles.icon]} />
				<Text style={styles.paymentText}>{data.name}</Text>
				{data.id === active && (
					<Image
						source={require('@/assets/images/icons/tick.png')}
						style={styles.tick}
					/>
				)}
			</View>
		</TouchableOpacity>
	);
};
PaymentMethod.displayName = 'PaymentMethod';

const styles = StyleSheet.create({
	btn: {
		width: '50%',
		padding: 4,
	},
	payment: {
		flexDirection: 'row',
		width: '100%',
		height: 45,
		backgroundColor: '#eee',
		borderRadius: 5,
		alignItems: 'center',
		borderColor: '#ddd',
		borderWidth: 3,
		paddingHorizontal: 8,
	},
	paymentText: {
		fontSize: 15,
		fontWeight: 'bold',
	},
	active: {
		borderColor: primary,
	},
	icon: {
		width: 35,
		height: 35,
		marginRight: 10,
	},
	tick: {
		width: 15,
		height: 15,
		position: 'absolute',
		top: 4,
		right: 4,
	},
});

export default PaymentMethod;
