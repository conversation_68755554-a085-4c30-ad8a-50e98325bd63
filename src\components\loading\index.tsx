import React, { memo } from 'react';
import { ActivityIndicator, StyleSheet, View, Text } from 'react-native';
import { useSelector } from 'react-redux';

import { getMessage, isLoading } from '@/store/App';
import primary from '@/constants/Colors';

const Loading = memo(() => {
	const loading = useSelector(isLoading);
	const message = useSelector(getMessage);

	if (loading) {
		return (
			<View style={styles.loading}>
				<View style={styles.loader}>
					<ActivityIndicator size="large" color={primary} />
					{message.length > 0 && <Text>{message}</Text>}
				</View>
			</View>
		);
	}
	return null;
});

Loading.displayName = 'Loading';

const styles = StyleSheet.create({
	loading: {
		position: 'absolute',
		top: 0,
		left: 0,
		width: '100%',
		height: '100%',
		zIndex: 99999,
		backgroundColor: 'rgba(0, 0, 0 ,.5)',
	},
	loader: {
		alignItems: 'center',
		flex: 1,
		justifyContent: 'center',
		top: '40%',
		position: 'absolute',
		width: '100%',
	},
});

export default Loading;
