import { createAsyncThunk } from '@reduxjs/toolkit';

import productsAPI from '@/services/Product';
import { setLoading } from '@/store/app';

const fetchProducts = createAsyncThunk(
	'products/fetchProducts',
	async (args: boolean, thunkApi) => {
		if (args) {
			thunkApi.dispatch(setLoading(true));
		}
		try {
			const response = await productsAPI.all();
			thunkApi.dispatch(setLoading(false));
			return Array.isArray(response.data) ? response.data : [];
		} catch (e) {
			console.log(e);
		}
		if (args) {
			thunkApi.dispatch(setLoading(false));
		}
		return thunkApi.rejectWithValue('Error');
	},
);

const fetchGifts = createAsyncThunk(
	'products/fetchGifts',
	async (args: boolean, thunkApi) => {
		if (args) {
			thunkApi.dispatch(setLoading(true));
		}
		try {
			const response = await productsAPI.getGifts();
			thunkApi.dispatch(setLoading(false));
			return Array.isArray(response.data) ? response.data : [];
		} catch (e) {
			console.log(e);
		}
		if (args) {
			thunkApi.dispatch(setLoading(false));
		}
		return thunkApi.rejectWithValue('Error');
	},
);

export { fetchProducts, fetchGifts };
