import React, { memo } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import isEqual from 'react-fast-compare';

import { currencyFormat } from '@/utils';
import { useAppDispatch } from '@/store';
import { IProduct } from '@/screens/Products/types';
import { addProduct } from '@/store/Product';

const SideBarItem = memo(({ data }: { data: IProduct }) => {
	const dispatch = useAppDispatch();

	const increment = () => {
		dispatch(
			addProduct({
				type: 'increment',
				item: data,
			}),
		);
	};

	const decrement = () => {
		dispatch(
			addProduct({
				type: 'decrement',
				item: data,
			}),
		);
	};

	return (
		<View style={styles.item}>
			<View style={styles.colName}>
				<Text>{data.name}</Text>
				<Text style={styles.totalPrice}>
					{currencyFormat(data.price * data.quantity)}
					<Text style={styles.price}> ({currencyFormat(data.price)})</Text>
				</Text>
			</View>
			<View style={styles.colAction}>
				<TouchableOpacity onPress={decrement} style={styles.btn}>
					<Text style={[styles.btnText, styles.btnDes]}>-</Text>
				</TouchableOpacity>
				<Text style={styles.quantity}>{data.quantity}</Text>
				<TouchableOpacity onPress={increment} style={styles.btn}>
					<Text style={styles.btnText}>+</Text>
				</TouchableOpacity>
			</View>
		</View>
	);
}, isEqual);

SideBarItem.displayName = 'ProductSidebarItem';

const styles = StyleSheet.create({
	item: {
		flexDirection: 'row',
		paddingVertical: 10,
	},
	totalPrice: {
		color: '#0a8b01',
	},
	price: {
		color: '#666',
		fontStyle: 'italic',
	},
	colName: {
		flex: 1,
	},
	colAction: {
		width: 110,
		flexDirection: 'row',
	},
	quantity: {
		width: 35,
		fontSize: 20,
		lineHeight: 30,
		textAlign: 'center',
	},
	btn: {
		borderColor: '#ddd',
		borderWidth: 1,
		width: 35,
		height: 35,
		borderRadius: 4,
	},
	btnText: {
		textAlign: 'center',
		fontSize: 30,
		lineHeight: 30,
	},
	btnDes: {},
});

export default SideBarItem;
