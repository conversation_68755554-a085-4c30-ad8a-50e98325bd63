﻿import React from 'react';
import {
	Text,
	StyleSheet,
	TouchableOpacity,
	View,
	Platform,
} from 'react-native';
import Icon from '@expo/vector-icons/Ionicons';
import { useSelector } from 'react-redux';
import { router } from 'expo-router';

import { getUser, setMenu } from '@/store/app';
import { useAppDispatch } from '@/store';

const HomeScreen = () => {
	const dispatch = useAppDispatch();
	const user = useSelector(getUser);

	const toCinema = () => {
		dispatch(setMenu('/pos/tickets'));
		router.replace('/pos/tickets');
	};

	const toProduct = () => {
		dispatch(setMenu('/pos/products'));
		router.replace('/pos/products');
	};

	const toPrint = () => {
		dispatch(setMenu('/pos/print'));
		router.replace('/pos/print');
	};

	const toMember = () => {
		dispatch(setMenu('/pos/members'));
		router.replace('/pos/members');
	};

	const toReport = () => {
		dispatch(setMenu('/pos/reports'));
		router.replace('/pos/reports');
	};

	const toTimelines = () => {
		dispatch(setMenu('/pos/timelines'));
		router.replace('/pos/timelines');
	};

	return (
		<View style={styles.bg}>
			<View style={styles.row}>
				{user?.permission.indexOf('can_ticket') >= 0 && (
					<TouchableOpacity style={styles.box} onPress={toCinema}>
						<Icon name="images-outline" size={70} />
						<Text style={styles.label}>Bán vé</Text>
					</TouchableOpacity>
				)}
				{user?.permission.indexOf('can_fastfood') >= 0 && (
					<TouchableOpacity style={styles.box} onPress={toProduct}>
						<Icon name="fast-food-outline" size={70} />
						<Text style={styles.label}>Bắp nước</Text>
					</TouchableOpacity>
				)}
				<TouchableOpacity style={styles.box} onPress={toPrint}>
					<Icon name="print-outline" size={70} />
					<Text style={styles.label}>In vé</Text>
				</TouchableOpacity>
			</View>
			<View style={styles.row}>
				{user?.permission?.indexOf('can_member') >= 0 && (
					<TouchableOpacity style={styles.box} onPress={toMember}>
						<Icon name="people-outline" size={70} />
						<Text style={styles.label}>Thành viên</Text>
					</TouchableOpacity>
				)}
				<TouchableOpacity style={styles.box} onPress={toTimelines}>
					<Icon name="calendar-outline" size={70} />
					<Text style={styles.label}>Lịch chiếu</Text>
				</TouchableOpacity>
				<TouchableOpacity style={styles.box} onPress={toReport}>
					<Icon name="newspaper-outline" size={70} />
					<Text style={styles.label}>Kết ca</Text>
				</TouchableOpacity>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		paddingHorizontal: Platform.OS === 'windows' ? 55 : 15,
		paddingVertical: 100,
	},
	row: {
		flexDirection: 'row',
		flex: 1,
	},
	box: {
		backgroundColor: '#fff',
		flex: 1,
		margin: 15,
		alignItems: 'center',
		justifyContent: 'center',
		elevation: 2,
	},
	label: {
		fontSize: 20,
		fontWeight: 'bold',
	},
});

export default HomeScreen;
