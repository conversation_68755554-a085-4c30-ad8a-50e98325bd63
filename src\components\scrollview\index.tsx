import React from 'react';
import {
	ScrollView as NativeScrollView,
	ScrollViewProps as NativeScrollViewProps,
	Platform,
} from 'react-native';

interface WebScrollViewProps {
	showScroller?: boolean;
}

type ScropViewProps = NativeScrollViewProps & WebScrollViewProps;

const ScrollView = React.memo((props: ScropViewProps) => {
	if (Platform.OS === 'web') {
		const ScrollContainer = require('react-indiana-drag-scroll').default;
		let className = 'scroll-container';

		if (props.showScroller) {
			className += ' scroll';
		}
		return (
			<ScrollContainer
				className={className}
				hideScrollbars={false}
				vertical={!props.horizontal}
				horizontal={props.horizontal}>
				{props.children}
			</ScrollContainer>
		);
	} else {
		return <NativeScrollView {...props} />;
	}
});

ScrollView.displayName = 'ScrollView';

export { NativeScrollView };

export default ScrollView;
