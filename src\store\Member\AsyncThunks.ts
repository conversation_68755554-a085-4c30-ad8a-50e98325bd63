﻿import { createAsyncThunk } from '@reduxjs/toolkit';
import { filter } from '@/services/Member';
import { setLoading } from '@/store/app';

export type SearchMemberData = {
	id: string;
	name: string;
	phone: string;
	identification: string;
};
export const fectchData = createAsyncThunk(
	'member/fectchData',
	async (args: SearchMemberData, thunkApi) => {
		thunkApi.dispatch(setLoading(true));
		try {
			const response = await filter(args);
			thunkApi.dispatch(setLoading(false));
			return response;
		} catch (e) {
			console.log(e);
		}
		thunkApi.dispatch(setLoading(false));
		return thunkApi.rejectWithValue('Error');
	},
);
