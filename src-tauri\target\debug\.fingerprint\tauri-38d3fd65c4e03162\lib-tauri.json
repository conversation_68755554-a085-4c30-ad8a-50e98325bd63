{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"unstable\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 2526866151816116117, "deps": [[40386456601120721, "percent_encoding", false, 5447697539743764811], [442785307232013896, "tauri_runtime", false, 5542784074252618259], [1200537532907108615, "url<PERSON><PERSON>n", false, 13145062201727086394], [3150220818285335163, "url", false, 15668146286072074414], [4143744114649553716, "raw_window_handle", false, 10079824973013050502], [4341921533227644514, "muda", false, 5658519361601763486], [4919829919303820331, "serialize_to_javascript", false, 16376259981249665629], [5986029879202738730, "log", false, 14669300167346157817], [7752760652095876438, "tauri_runtime_wry", false, 7586439427620598238], [8539587424388551196, "webview2_com", false, 11566940673542702221], [9010263965687315507, "http", false, 14314634951049504858], [9228235415475680086, "tauri_macros", false, 3376428685323327640], [9538054652646069845, "tokio", false, 17307833443289394789], [9689903380558560274, "serde", false, 10697150404045262645], [9920160576179037441, "getrandom", false, 14994944675784983706], [10229185211513642314, "mime", false, 12879061754667572319], [10629569228670356391, "futures_util", false, 5778955098311341807], [10755362358622467486, "build_script_build", false, 7669751951434522486], [10806645703491011684, "thiserror", false, 11115062776651022174], [11050281405049894993, "tauri_utils", false, 3638413504225201697], [11989259058781683633, "dunce", false, 8003502364352757275], [12565293087094287914, "window_vibrancy", false, 5498509458035670102], [12986574360607194341, "serde_repr", false, 16245819982407033116], [13077543566650298139, "heck", false, 290801593150339784], [13625485746686963219, "anyhow", false, 12400837003654224066], [14585479307175734061, "windows", false, 15772416081231344459], [15367738274754116744, "serde_json", false, 12445351040836002554], [16928111194414003569, "dirs", false, 9511758020444443471], [17155886227862585100, "glob", false, 10599412529121352411]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-38d3fd65c4e03162\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}