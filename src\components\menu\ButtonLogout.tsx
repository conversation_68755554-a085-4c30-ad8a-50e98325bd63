import Icon from '@expo/vector-icons/Ionicons';

import MenuItem from './MenuItem';
import Alert from '@/components/Alert';
import { logout } from '@/store/App';
import { useAppDispatch } from '@/store';

const ButtonLogout = () => {
	const dispatch = useAppDispatch();

	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon size={size} name="exit-outline" color={color} />
	);
	const onPress = () => {
		Alert.alert('Exit', 'Bạn có chắc muốn đăng xuất tài khoản?', [
			{
				text: 'Hủy',
				style: 'cancel',
			},
			{ text: 'Đăng xuất', onPress: onLogout },
		]);
	};

	const onLogout = () => {
		dispatch(logout());
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Đăng xuất'}
			isFocused={false}
		/>
	);
};

export default ButtonLogout;
