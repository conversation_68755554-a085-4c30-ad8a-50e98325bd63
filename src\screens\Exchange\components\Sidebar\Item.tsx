import React, { memo } from 'react';
import { Text, View, TouchableOpacity, StyleSheet, Image } from 'react-native';
import isEqual from 'react-fast-compare';

import { addGift } from '@/store/cart/Slice';
import { useAppDispatch } from '@/store';
import { IGift } from '@/types/Cart';

type ItemProps = {
	data: IGift;
};

const Item = memo(({ data }: ItemProps) => {
	const dispatch = useAppDispatch();

	const increment = () => {
		dispatch(
			addGift({
				type: 'increment',
				item: data,
			}),
		);
	};

	const decrement = () => {
		dispatch(
			addGift({
				type: 'decrement',
				item: data,
			}),
		);
	};

	return (
		<View style={styles.item}>
			<View>
				<Image source={{ uri: data.image }} style={styles.image} />
			</View>
			<View style={styles.colName}>
				<Text>{data.name}</Text>
				<Text>{data.point} di?m</Text>
			</View>
			<View style={styles.colAction}>
				<TouchableOpacity onPress={decrement} style={styles.btn}>
					<Text style={[styles.btnText, styles.btnDes]}>-</Text>
				</TouchableOpacity>
				<Text style={styles.quantity}>{data.quantity}</Text>
				<TouchableOpacity onPress={increment} style={styles.btn}>
					<Text style={styles.btnText}>+</Text>
				</TouchableOpacity>
			</View>
		</View>
	);
}, isEqual);

Item.displayName = 'ExchangeSidebarItem';

const styles = StyleSheet.create({
	item: {
		flexDirection: 'row',
		paddingVertical: 10,
	},
	image: {
		width: 60,
		height: 60,
		borderRadius: 60,
		borderColor: '#999',
		borderWidth: 1,
		marginRight: 8,
	},
	totalPrice: {
		color: '#333',
	},
	price: {
		color: '#666',
		fontStyle: 'italic',
	},
	colName: {
		flex: 1,
	},
	colAction: {
		width: 110,
		flexDirection: 'row',
	},
	quantity: {
		width: 35,
		fontSize: 20,
		lineHeight: 30,
		textAlign: 'center',
	},
	btn: {
		borderColor: '#ddd',
		borderWidth: 1,
		width: 35,
		height: 35,
		borderRadius: 4,
	},
	btnDes: {},
	btnText: {
		textAlign: 'center',
		fontSize: 30,
		lineHeight: 32,
	},
});

export default Item;
