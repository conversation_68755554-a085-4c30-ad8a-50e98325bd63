﻿import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';

import { IProduct } from '../../types';
import FlatList from '@/components/flat-list';
import Item from './Item';
import { getCombo } from '@/store/product';
import Result from './Result';
import Configs from '@/constants/App';
import ProductGroup from '../ProductGroup';
import { getConfigs } from '@/store/app';
import primary from '@/constants/Colors';

const Combo = React.memo(() => {
	const configs = useSelector(getConfigs);

	const [all, setAll] = useState<boolean>(!configs.group_combo);

	const data = useSelector(getCombo);

	const drinks = data.filter((item) => item.type === 'drink');
	const popcorns = data.filter((item) => item.type === 'popcorn');

	const toggle = () => {
		setAll(!all);
	};

	const renderItem = ({ item }: { item: IProduct }) => <Item data={item} />;

	if (!all) {
		return (
			<View style={styles.wrap}>
				<View style={styles.wrap}>
					<View style={styles.row}>
						<Text style={styles.label}>
							<Text style={styles.step}>1</Text> Chọn nước
						</Text>
						<FlatList
							keyExtractor={(item) => item.id}
							renderItem={renderItem}
							data={drinks}
							horizontal
						/>
					</View>
					<View style={styles.row}>
						<Text style={styles.label}>
							<Text style={styles.step}>2</Text> Chọn bắp/snack
						</Text>
						<FlatList
							keyExtractor={(item) => item.id}
							renderItem={renderItem}
							data={popcorns}
							horizontal
						/>
					</View>
					<View style={styles.row}>
						<Text style={styles.label}>
							<Text style={styles.step}>3</Text> Chọn combo
						</Text>
						<Result />
					</View>
				</View>
				<View style={styles.action}>
					<TouchableOpacity style={styles.btn} onPress={toggle}>
						<Text style={styles.btnLabel}>Hiện tất cả</Text>
					</TouchableOpacity>
				</View>
			</View>
		);
	}
	return (
		<View style={styles.wrap}>
			<View style={styles.wrap}>
				<ProductGroup id={Configs.combo_id} combo={false} />
			</View>
			<View style={styles.action}>
				<TouchableOpacity style={styles.btn} onPress={toggle}>
					<Text style={styles.btnLabel}>Nhóm combo</Text>
				</TouchableOpacity>
			</View>
		</View>
	);
});

Combo.displayName = 'Combo';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
	},
	row: {},
	label: {
		fontSize: 18,
		fontWeight: 'bold',
		paddingVertical: 5,
	},
	step: {
		borderWidth: 2,
		borderColor: primary,
		borderRadius: 30,
		width: 25,
		height: 25,
		paddingHorizontal: 7,
		textAlign: 'center',
		color: primary,
	},
	action: {
		paddingVertical: 8,
		alignItems: 'center',
		height: 50,
	},
	btn: {
		paddingHorizontal: 10,
		paddingVertical: 3,
		borderRadius: 5,
		borderWidth: 1,
		borderColor: primary,
	},
	btnLabel: {
		fontSize: 16,
		color: primary,
	},
});

export default Combo;
