import React, { useEffect } from 'react';
import { Text, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

import Modal from '@/components/Modal';
import { isShowSuccess, setModalSuccess } from '@/store/App';
import { useAppDispatch } from '@/store';
import { Image, ImageBackground } from '@/components/Image';

const WelcomeScreen = () => {
	const visible = useSelector(isShowSuccess);
	const dispatch = useAppDispatch();

	useEffect(() => {
		if (visible) {
			let timer = setTimeout(() => {
				dispatch(setModalSuccess(false));
			}, 5000);
			return () => {
				clearTimeout(timer);
			};
		}
	}, [visible]); // eslint-disable-line react-hooks/exhaustive-deps

	return (
		<Modal visible={visible} style={styles.modal} closeBtn={false}>
			<ImageBackground
				style={styles.bg}
				source={require('@/assets/images/backgrounds/bg.jpg')}>
				<Image
					source={require('@/assets/images/order-success.png')}
					style={styles.image}
				/>
				<Text style={styles.text1}>Thanh toán thành công</Text>
				<Text style={[styles.text1, styles.text2]}>
					Chúc quý khách xem phim vui vẻ
				</Text>
			</ImageBackground>
		</Modal>
	);
};

const styles = StyleSheet.create({
	modal: {
		flex: 1,
		width: '100%',
		padding: 0,
		margin: 0,
	},
	bg: {
		width: '100%',
		height: '100%',
		backgroundColor: 'blue',
		flex: 1,
		alignContent: 'center',
		alignItems: 'center',
		justifyContent: 'center',
	},
	image: {
		width: 400,
		height: 481,
		marginBottom: 30,
	},
	text1: {
		fontSize: 35,
		color: '#fff',
		marginBottom: 10,
	},
	text2: {
		fontSize: 20,
	},
});
export default WelcomeScreen;
