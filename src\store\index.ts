﻿import { configureStore } from '@reduxjs/toolkit';
import { useDispatch } from 'react-redux';

import BookingSlice from '@/store/booking/Slice';
import ProductsSlice from '@/store/product/Slice';
import CartSlice from '@/store/cart/Slice';
import MemberSlice from '@/store/member/Slice';
import AppSlice from '@/store/app/Slice';

const store = configureStore({
	reducer: {
		app: AppSlice,
		booking: BookingSlice,
		products: ProductsSlice,
		cart: CartSlice,
		member: MemberSlice,
	},
	middleware: (getDefaultMiddleware) =>
		getDefaultMiddleware({
			immutableCheck: false,
		}),
});

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;

export const useAppDispatch: () => AppDispatch = useDispatch;

export default store;
