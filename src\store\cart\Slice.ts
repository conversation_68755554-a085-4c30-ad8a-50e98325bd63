import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from '@/utils/Toast';

import sendToCustomer from '@/services/Broadcast';
import { RootState } from '@/store';
import { CartState } from '@/types/Cart';
import {
	clearOrder,
	createOrder,
	payWithQrCode,
	setMemberOrder,
} from './AsyncThunks';

const initialState: CartState = {
	tickets: [],
	products: [],
	amount: {
		ticket: 0,
		combo: 0,
		total: 0,
		decrease: 0,
	},
	order_id: 0,
	bill: '',
	money: 0,
	payment_methods: [
		{
			id: '0',
			name: 'Tiền mặt',
		},
		{
			id: '1',
			name: 'Quẹt thẻ',
		},
		{
			id: 'momo',
			name: '<PERSON><PERSON><PERSON>',
		},
		{
			id: 'vnpay',
			name: 'VNPay',
		},
		{
			id: 'shopeepay',
			name: 'ShopeePay',
		},
		{
			id: 'grab',
			name: '<PERSON>rab',
		},
	],
	payment: '0',
	member: null,
	gifts: [],
	usedPoint: 0,
	qrcode: undefined,
};

export const CartSlice = createSlice({
	name: 'cart',
	initialState,
	reducers: {
		resetCart: (state) => {
			state.tickets = [];
			state.products = [];
			state.gifts = [];
			state.usedPoint = 0;
			state.order_id = 0;
			state.money = 0;
		},
		setCart: (state, action) => {
			state.tickets = action.payload.tickets;
			state.products = action.payload.products;
			state.amount = action.payload.amount;
			state.order_id = action.payload.order_id;
			state.bill = action.payload.bill;
		},
		setTickets: (state, action) => {
			state.tickets = action.payload;
		},
		setAmount: (state, action) => {
			state.amount = {
				...state.amount,
				...action.payload,
			};
			sendToCustomer({
				action: 'setAmount',
				payload: action.payload,
			});
		},
		setPaymentMethods: (state, action) => {
			state.payment_methods = action.payload;
			sendToCustomer({
				action: 'setPaymentMethods',
				payload: action.payload,
			});
		},
		setPaymentMethod: (state, action) => {
			state.payment = action.payload;
			sendToCustomer({
				action: 'setPayment',
				payload: action.payload,
			});
		},
		setMoney: (state, action) => {
			state.money = action.payload;
			sendToCustomer({
				action: 'setMoney',
				payload: action.payload,
			});
		},
		addMoney: (state, action) => {
			state.money += action.payload;
			sendToCustomer({
				action: 'setMoney',
				payload: state.money,
			});
		},
		setMember: (state, action) => {
			state.member = action.payload;
			sendToCustomer({
				action: 'setMember',
				payload: action.payload,
			});
		},
		setOrderPoint: (state, action) => {
			if (state.member) {
				state.member.point = action.payload;
			}
		},
		addGift: (state, action) => {
			if (state.member) {
				if (state.member.point_left && state.member.point_left > 0) {
					let checkItem = state.gifts.findIndex(
						(item) => item.id === action.payload.item.id,
					);
					if (checkItem >= 0) {
						if (
							action.payload.type === 'increment' &&
							typeof action.payload.item.showtime_id === 'undefined'
						) {
							if (
								state.member.point_left -
									state.usedPoint -
									action.payload.item.point >=
								0
							) {
								++state.gifts[checkItem].quantity;
								state.usedPoint += action.payload.item.point;
							} else {
								showMessage({
									message: 'Thành viên không đủ điểm',
									type: 'danger',
								});
							}
						} else {
							--state.gifts[checkItem].quantity;
							state.usedPoint -= action.payload.item.point;
							if (state.gifts[checkItem].quantity < 1) {
								state.gifts.splice(checkItem, 1);
							}
						}
					} else {
						if (
							state.member.point_left -
								state.usedPoint -
								action.payload.item.point >=
							0
						) {
							state.gifts.push({
								...action.payload.item,
								quantity: 1,
							});
							state.usedPoint += action.payload.item.point;
						} else {
							showMessage({
								message: 'Thành viên không đủ điểm',
								type: 'danger',
							});
						}
					}
				} else {
					showMessage({
						message: 'Thành viên không đủ điểm',
						type: 'danger',
					});
				}
			}
		},
		setGifts: (state, action) => {
			state.gifts = action.payload.gifts || [];
			state.usedPoint = action.payload.usedPoint || 0;
			sendToCustomer({
				action: 'setGifts',
				payload: action.payload,
			});
		},
		updateExchangeTickets: (state, action) => {
			state.tickets = state.tickets.map((item) => {
				if (action.payload.includes(item.seat_name)) {
					state.amount.total -= item.price;
					state.amount.ticket -= item.price;
					item.price = 0;
					item.target = 'Đổi điểm';
				}
				return item;
			});
		},
		setQrcode: (state, action) => {
			state.qrcode = action.payload;
			sendToCustomer({
				action: 'setQrcode',
				payload: action.payload,
			});
		},
	},
	extraReducers: (builder) => {
		builder.addCase(createOrder.fulfilled, (state, action) => {
			if (action.payload) {
				state.tickets = action.payload.tickets;
				state.products = action.payload.products;
				state.amount = action.payload.amount;
				state.order_id = action.payload.order_id;
				state.bill = action.payload.bill;
				state.payment = '0';
				// state.payment_methods = action.payload.payment_methods;
				// sendToCustomer({
				//   action: 'setPaymentMethods',
				//   payload: action.payload.payment_methods,
				// });
			}
		});
		builder.addCase(setMemberOrder.fulfilled, (state, action) => {
			if (state.member) {
				state.member.point = action.payload;
				sendToCustomer({
					action: 'setOrderPoint',
					payload: action.payload,
				});
			}
		});
		builder.addCase(payWithQrCode.fulfilled, (state, action) => {
			if (action.payload) {
				state.qrcode = action.payload;
				sendToCustomer({
					action: 'setQrcode',
					payload: action.payload,
				});
			}
		});
		builder.addCase(clearOrder.fulfilled, (state) => {
			state.order_id = 0;
			state.amount = {
				ticket: 0,
				combo: 0,
				total: 0,
				decrease: 0,
			};
			state.tickets = [];
			state.products = [];
			state.bill = '';
			state.payment = '0';
			state.member = null;
			state.gifts = [];
			state.usedPoint = 0;
			sendToCustomer({
				action: 'clearOrder',
			});
		});
	},
});

export const {
	resetCart,
	setCart,
	setPaymentMethod,
	setPaymentMethods,
	setMoney,
	addMoney,
	setAmount,
	setMember,
	addGift,
	setGifts,
	setOrderPoint,
	updateExchangeTickets,
	setQrcode,
} = CartSlice.actions;

export const getAmount = (state: RootState) => state.cart.amount;
export const getMoney = (state: RootState) => state.cart.money;
export const getMember = (state: RootState) => state.cart.member;

export default CartSlice.reducer;
