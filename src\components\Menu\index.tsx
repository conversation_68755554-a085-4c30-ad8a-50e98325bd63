﻿import { StyleSheet, View, Text } from 'react-native';
import { useSelector } from 'react-redux';
import Icon from '@expo/vector-icons/Ionicons';
import { router, usePathname } from 'expo-router';

import { getUser } from '@/store/app';
import primary from '@/constants/Colors';

import Clock from '@/components/Clock';
import ButtonLogout from './ButtonLogout';
import ButtonFullscreen from './ButtonFullscreen';
import ButtonReload from './ButtonReload';
import ButtonExit from './ButtonExit';
import MenuItem from './MenuItem';

type MenuPath =
	| '/pos'
	| '/pos/tickets'
	| '/pos/products'
	| '/pos/print'
	| '/pos/members'
	| '/pos/timelines'
	| '/pos/reports'
	| '/pos/money'
	| '/pos/settings'
	| '/pos/manager';

type Item = {
	icon: keyof typeof Icon.glyphMap;
	label: string;
	to: MenuPath;
	permission?: string;
};
const menus: Item[] = [
	{
		icon: 'home',
		label: 'Trang chủ',
		to: '/pos',
	},
	{
		icon: 'images-outline',
		label: 'Bán vé',
		to: '/pos/tickets',
		permission: 'can_ticket',
	},
	{
		icon: 'fast-food-outline',
		label: 'Bắp nước',
		to: '/pos/products',
		permission: 'can_fastfood',
	},
	{
		icon: 'print-outline',
		label: 'In vé',
		to: '/pos/print',
	},
	{
		icon: 'people-outline',
		label: 'Thành viên',
		to: '/pos/members',
		permission: 'can_member',
	},
	{
		icon: 'calendar-outline',
		label: 'Lịch chiếu',
		to: '/pos/timelines',
	},
	{
		icon: 'newspaper-outline',
		label: 'Kết ca',
		to: '/pos/reports',
	},
	{
		icon: 'cash-outline',
		label: 'Kê tiền',
		to: '/pos/money',
	},
	{
		icon: 'settings-outline',
		label: 'Cài đặt',
		to: '/pos/settings',
		permission: 'setting',
	},
	{
		icon: 'person-outline',
		label: 'Manager',
		to: '/pos/manager',
	},
];

const hidden = [
	'/pos/booking',
	'/pos/checkout',
	'/pos/exchange',
	'/pos/booking-product',
];

function Menu() {
	const user = useSelector(getUser);
	const pathname = usePathname();

	if (hidden.includes(pathname)) {
		return null;
	}

	return (
		<View style={styles.wrapper}>
			<View style={styles.header}>
				<View style={styles.logo}>
					<Text numberOfLines={1} style={styles.username}>
						{user.name}
					</Text>
					<Clock />
				</View>
			</View>
			<View style={styles.menu}>
				{menus.map((item) => {
					if (
						!item.permission ||
						user.permission.indexOf(item.permission) >= 0
					) {
						const onPress = () => {
							router.replace(item.to);
						};

						return (
							<MenuItem
								iconName={item.icon}
								onPress={onPress}
								label={item.label}
								key={item.icon}
								isFocused={item.to === pathname}
							/>
						);
					}
					return null;
				})}
				<ButtonLogout />
			</View>
			<View style={styles.footer}>
				<View>
					<ButtonExit />
					<ButtonReload />
					<ButtonFullscreen />
				</View>
			</View>
		</View>
	);
}

const styles = StyleSheet.create({
	wrapper: {
		backgroundColor: primary,
		minWidth: 0,
		boxShadow: '1px 0 5px rgba(0, 0, 0, .5)',
	},
	header: {},
	menu: {},
	logo: {
		backgroundColor: '#ca1864',
		alignItems: 'center',
		justifyContent: 'center',
		paddingVertical: 5,
	},
	username: {
		color: '#eee',
		fontSize: 12,
		paddingVertical: 2,
	},
	footer: {
		justifyContent: 'flex-end',
		flex: 1,
	},
});

export default Menu;
