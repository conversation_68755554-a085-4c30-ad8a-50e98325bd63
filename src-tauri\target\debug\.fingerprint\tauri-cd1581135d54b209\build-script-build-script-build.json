{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"unstable\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 6459596045160684920, "deps": [[283161442353679854, "tauri_build", false, 3581424779148684685], [11050281405049894993, "tauri_utils", false, 8694799301641733153], [13077543566650298139, "heck", false, 290801593150339784], [17155886227862585100, "glob", false, 10599412529121352411]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-cd1581135d54b209\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}