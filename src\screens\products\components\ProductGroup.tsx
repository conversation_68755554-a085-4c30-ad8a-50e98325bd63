import React, { memo } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';

import Item from './ProductItem';
import { getGroupData } from '@/store/Product/Selector';
import FlatList from '@/components/FlatList';
import Combo from './Combo';
import { IProduct } from '@/types/Product';
import Configs from '@/constants/App';

const ProductGroup = memo(
	({ id, combo = true }: { id: number; combo?: boolean }) => {
		const products = useSelector(getGroupData(id));

		const renderItem = ({ item }: { item: IProduct }) => <Item data={item} />;

		if (combo && id === Configs.combo_id && products) {
			return <Combo />;
		}

		if (products) {
			return (
				<View style={styles.list}>
					<FlatList
						keyExtractor={(item) => item.id}
						renderItem={renderItem}
						data={products.data}
						numColumns={4}
					/>
				</View>
			);
		}
		return null;
	},
);
ProductGroup.displayName = 'ProductGroup';

const styles = StyleSheet.create({
	list: {
		flex: 1,
		borderRadius: 20,
		marginHorizontal: -2,
		overflow: 'hidden',
	},
});

export default ProductGroup;
