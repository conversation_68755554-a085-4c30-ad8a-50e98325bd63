# Comprehensive script to fix ALL component import paths

Write-Host "Fixing ALL component import paths..."

# Get all TypeScript and JavaScript files
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts", "*.tsx", "*.js", "*.jsx"

$totalFixed = 0

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # Fix ALL component import paths to use proper PascalCase
    $content = $content -replace '@/components/alert\b', '@/components/Alert'
    $content = $content -replace '@/components/button\b', '@/components/Button'
    $content = $content -replace '@/components/image\b', '@/components/Image'
    $content = $content -replace '@/components/input\b', '@/components/Input'
    $content = $content -replace '@/components/loading\b', '@/components/Loading'
    $content = $content -replace '@/components/menu\b', '@/components/Menu'
    $content = $content -replace '@/components/modal\b', '@/components/Modal'
    $content = $content -replace '@/components/topbar\b', '@/components/Topbar'
    $content = $content -replace '@/components/flatlist\b', '@/components/FlatList'
    $content = $content -replace '@/components/scrollview\b', '@/components/ScrollView'
    
    # Fix subpath imports
    $content = $content -replace '@/components/button/', '@/components/Button/'
    $content = $content -replace '@/components/input/', '@/components/Input/'
    
    # Write back file if there are changes
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
        Write-Host "Fixed: $relativePath"
        $totalFixed++
    }
}

Write-Host "Fixed $totalFixed files."
Write-Host "All component import paths should now be correct!"

# Run ESLint to check
Write-Host ""
Write-Host "Running ESLint to verify fixes..."
try {
    $eslintResult = npx eslint src --quiet 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "No ESLint errors remaining!"
    } else {
        Write-Host "Some ESLint errors still remain:"
        $eslintResult | Select-Object -First 10
    }
} catch {
    Write-Host "Could not run ESLint"
}
