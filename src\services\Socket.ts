import { useRef } from 'react';
import { useDispatch } from 'react-redux';
// @ts-ignore
import io from 'socket.io-client';

import { updateSeat } from '@/store/booking';

const useSocket = (Url: string) => {
	const socket = useRef<any>(null);

	const dispatch = useDispatch();

	const connect = () => {
		if (socket.current !== null) {
			socket.current.close();
		}
		socket.current = io.connect(Url, {
			reconnectionAttempts: 5,
		});

		socket.current.on('connect_error', (e: any) => {
			console.log(e);
		});

		socket.current.on('updateSeat', (seat: any) => {
			console.log(seat);
			dispatch(updateSeat(seat));
		});
	};

	const close = () => {
		if (socket.current) {
			socket.current.close();
		}
	};

	return { socket, connect, close };
};

export default useSocket;
