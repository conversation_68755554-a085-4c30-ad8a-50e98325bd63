﻿import { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { Redirect, Stack, usePathname } from 'expo-router';
import { useSelector } from 'react-redux';

import { getUser } from '@/store/app';
import { AlertProvider } from '@/components/alert';
import Toast from '@/utils/Toast';
import sendToCustomer from '@/services/Broadcast';
import Menu from '@/components/menu';

export default function POSLayout() {
	const user = useSelector(getUser);

	const pathname = usePathname();

	useEffect(() => {
		sendToCustomer({
			route: pathname.replace('/pos', ''),
		});
	}, [pathname]);

	if (!user.name) {
		return <Redirect href="/" />;
	}

	return (
		<View style={styles.bg}>
			<Menu />
			<Stack screenOptions={{ headerShown: false }}>
				<Stack.Screen
					name="booking-products"
					options={{
						presentation: 'modal',
					}}
				/>
			</Stack>
			<Toast visibilityTime={2500} topOffset={20} />
			<AlertProvider />
		</View>
	);
}

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
	},
});
