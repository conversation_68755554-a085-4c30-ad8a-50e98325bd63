import React from 'react';
import { StyleProp, StyleSheet, ViewStyle } from 'react-native';
import { Text, PlatformPressable } from '@react-navigation/elements';
import Icon from '@expo/vector-icons/Ionicons';

import primary from '@/constants/Colors';

type Props = {
	icon?: (props: any) => React.ReactNode;
	iconName?: keyof typeof Icon.glyphMap;
	onPress: () => void;
	href?: string;
	label: string;
	isFocused: boolean;
	style?: StyleProp<ViewStyle>;
};
const iconSize = 26;

const MenuItem = React.memo(
	({ icon, iconName, onPress, href, isFocused, label, style }: Props) => {
		return (
			<PlatformPressable
				onPress={onPress}
				href={href}
				style={[style, styles.button, isFocused && styles.buttonActive]}>
				{icon && icon({ color: isFocused ? primary : '#fff', size: iconSize })}
				{iconName && (
					<Icon
						name={iconName}
						size={iconSize}
						color={isFocused ? primary : '#fff'}
					/>
				)}
				<Text
					style={[styles.label, isFocused && styles.labelActive]}
					numberOfLines={1}>
					{label}
				</Text>
			</PlatformPressable>
		);
	},
);

MenuItem.displayName = 'MenuItem';

const styles = StyleSheet.create({
	button: {
		flex: 1,
		padding: 2,
		borderRadius: 8,
		alignContent: 'center',
		justifyContent: 'center',
		alignItems: 'center',
		marginHorizontal: 4,
		marginVertical: 3,
	},
	buttonActive: {
		backgroundColor: '#fff',
	},
	label: {
		fontSize: 10,
		textAlign: 'center',
		color: 'white',
		letterSpacing: -0.2,
		marginTop: -3,
	},
	labelActive: {
		color: primary,
	},
});
export default MenuItem;
