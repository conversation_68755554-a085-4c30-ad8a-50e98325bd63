import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import isEqual from 'react-fast-compare';

import { ICartTicket } from '@/types/Cart';
import { currencyFormat } from '@/utils';

type Props = {
	item: ICartTicket;
};

const TicketItem = React.memo(({ item }: Props) => {
	return (
		<View style={styles.item}>
			<Text style={styles.name}>
				{item.seat_name} - <Text style={styles.quantity}>{item.target}</Text>
			</Text>
			<Text style={styles.price}>{currencyFormat(item.price)} đ</Text>
		</View>
	);
}, isEqual);

TicketItem.displayName = 'TicketItem';

const styles = StyleSheet.create({
	item: {
		marginVertical: 5,
	},
	name: {
		fontSize: 16,
	},
	quantity: {
		color: 'red',
		fontSize: 13,
	},
	price: {
		color: 'green',
	},
});

export default TicketItem;
