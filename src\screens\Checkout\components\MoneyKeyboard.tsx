import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch } from 'react-redux';

import { addMoney, setMoney } from '@/store/cart/Slice';

const MoneyKeyboard = React.memo(() => {
	const dispatch = useDispatch();

	const onPress = (value: number) => () => {
		dispatch(addMoney(value));
	};

	const reset = () => {
		dispatch(setMoney(0));
	};

	return (
		<View style={styles.bg}>
			<View style={styles.row}>
				<Button onPress={onPress(500000)} title="500.000" />
				<Button onPress={onPress(200000)} title="200.000" />
			</View>
			<View style={styles.row}>
				<Button onPress={onPress(100000)} title="100.000" />
				<Button onPress={onPress(50000)} title="50.000" />
			</View>
			<View style={styles.row}>
				<Button onPress={onPress(20000)} title="20.000" />
				<Button onPress={onPress(10000)} title="10.000" />
			</View>
			<View style={styles.row}>
				<Button onPress={onPress(5000)} title="5000" />
				<Button onPress={onPress(2000)} title="2000" />
			</View>
			<View style={styles.row}>
				<Button onPress={onPress(1000)} title="1000" />
				<Button onPress={onPress(500)} title="500" />
			</View>
			<View style={styles.row}>
				<Button onPress={reset} title="DEL" />
			</View>
		</View>
	);
});

MoneyKeyboard.displayName = 'MoneyKeyboard';

type TButton = {
	onPress: () => void;
	title: string;
};
const Button = React.memo(({ onPress, title }: TButton) => {
	return (
		<TouchableOpacity style={styles.btn} onPress={onPress}>
			<Text style={styles.btnLabel}>{title}</Text>
		</TouchableOpacity>
	);
});

Button.displayName = 'MoneyKeyboardButton';

const styles = StyleSheet.create({
	bg: {
		paddingVertical: 10,
	},
	row: {
		flexDirection: 'row',
	},
	btn: {
		flex: 1,
		backgroundColor: '#e5e5e5',
		borderColor: '#b9b6b6',
		borderWidth: 2,
		paddingVertical: 10,
		paddingHorizontal: 10,
		margin: 4,
		borderRadius: 3,
	},
	btnLabel: {
		fontSize: 18,
		fontWeight: 'bold',
		textAlign: 'center',
	},
});

export default MoneyKeyboard;
