import React, { useCallback, useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';

import useSocket from '@/services/Socket';
import { currentShowtime, resetMap } from '@/store/booking/Slice';

import Maps from './components/Maps';
import Sidebar from './components/Sidebar';
import HeaderBar from './components/HeaderBar';
import { useAppDispatch } from '@/store';
import { getShowtime } from '@/store/booking';
import Configs from '@/constants/App';

const BookingScreen = () => {
	const dispatch = useAppDispatch();
	const socket = useSocket(Configs.socket_url);

	const showtime = useSelector(currentShowtime);

	const [ready, setReady] = useState(false);

	useFocusEffect(
		useCallback(() => {
			socket.connect();
			return () => {
				socket.close();
			};
		}, []), // eslint-disable-line react-hooks/exhaustive-deps
	);

	useEffect(() => {
		dispatch(resetMap());
		setReady(true);
		if (showtime) {
			getData(showtime.id);
		}
	}, [showtime]); // eslint-disable-line react-hooks/exhaustive-deps

	const getData = async (id: string) => {
		dispatch(getShowtime(id));
	};

	return (
		<View style={styles.bg}>
			<HeaderBar />
			<View style={styles.flex}>
				{ready && <Maps />}
				<View style={styles.sidebar}>
					<Sidebar />
				</View>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	flex: {
		flexDirection: 'row',
		flex: 1,
		justifyContent: 'flex-end',
	},
	sidebar: {
		width: 250,
		backgroundColor: '#fff',
		marginTop: -45,
		borderTopLeftRadius: 15,
		overflow: 'hidden',
		boxShadow: '0 0 10px rgba(0, 0, 0, .3)',
	},
});

export default BookingScreen;
