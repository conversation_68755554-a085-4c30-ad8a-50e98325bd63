import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/store';

const BookingSelector = (state: RootState) => state.booking;

export const getSeat = (id: number) => {
	return createSelector(BookingSelector, (booking) =>
		booking.maps.find((item) => item.id === id),
	);
};

export const getSelected = createSelector(
	BookingSelector,
	(booking) => booking.seats,
);

export const getPrices = createSelector(
	BookingSelector,
	(booking) => booking.prices,
);
