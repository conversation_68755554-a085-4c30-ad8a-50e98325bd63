import React, { memo, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';

import { getCombo, getGroupData } from '@/store/product';

import FlatList from '@/components/flat-list';
import Item from '../ProductItem';

import Configs from '@/constants/App';
import { IProduct } from '@/types/Product';

const Result = memo(() => {
	const data = useSelector(getCombo);
	const products = useSelector(getGroupData(Configs.combo_id));

	const selected = data.filter((item) => item.quantity > 0);

	const combo = useMemo(() => {
		if (products && products.data.length > 0) {
			return products.data.filter((product) => {
				const desc: string = product.description
					? product.description.toUpperCase()
					: product.name;
				for (const item of selected) {
					if (!desc.includes(item.name.toUpperCase())) {
						return false;
					}
				}
				return true;
			});
		}
		return [];
	}, [products, selected]);

	const renderItem = ({ item }: { item: IProduct }) => (
		<Item data={item} width={150} />
	);

	return (
		<View style={styles.wrap}>
			<FlatList
				keyExtractor={(item) => item.id}
				renderItem={renderItem}
				data={combo}
				horizontal
			/>
		</View>
	);
});

Result.displayName = 'ComboResult';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
	},
});

export default Result;
