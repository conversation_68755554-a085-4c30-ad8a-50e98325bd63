import React from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';
import { ITabItem } from '../types';

type TabItemProps = {
	active?: boolean;
	data: ITabItem;
	onPress(date: string): void;
};

const TabItem = React.memo(
	({ active = false, data, onPress = () => {} }: TabItemProps) => {
		const onChoose = () => {
			onPress(data.date);
		};
		return (
			<TouchableOpacity
				onPress={onChoose}
				style={[styles.tab, active ? styles.active : null]}>
				<Text style={styles.label}>{data.title}</Text>
				<Text style={[styles.label, styles.date]}>{data.day}</Text>
				<Text style={styles.label}>{data.month}</Text>
			</TouchableOpacity>
		);
	},
);

TabItem.displayName = 'TabItem';

const styles = StyleSheet.create({
	tab: {
		width: 120,
		alignItems: 'center',
		padding: 8,
		borderRadius: 10,
		margin: 5,
		backgroundColor: '#fff',
	},
	active: {
		backgroundColor: '#ffae00',
	},
	label: {
		fontSize: 18,
		lineHeight: 18,
		color: '#000',
	},
	date: {
		fontSize: 35,
		lineHeight: 35,
		fontWeight: 'bold',
	},
});

export default TabItem;
