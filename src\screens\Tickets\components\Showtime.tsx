import React from 'react';
import {
	StyleSheet,
	Text,
	TextStyle,
	TouchableOpacity,
	ViewStyle,
} from 'react-native';
import { useSelector } from 'react-redux';
import isEqual from 'react-fast-compare';
import { router } from 'expo-router';

import { IShowtime } from '../types';
import { useAppDispatch } from '@/store';
import { currentShowtime, setShowtime } from '@/store/Booking';
import sendToCustomer from '@/services/Broadcast';
import Alert from '@/components/Alert';
import { showMessage } from '@/utils/Toast';
import primary from '@/constants/Colors';
import { getDate } from '@/utils';

interface ShowtimeProps {
	showtime: IShowtime;
	poster: string;
	name: string;
	age: string;
}

const currentDate: string = getDate();
const currentY: string = new Date().getFullYear().toString();

const Showtime = React.memo(
	({ showtime, poster, name, age }: ShowtimeProps) => {
		const dispatch = useAppDispatch();
		const current = useSelector(currentShowtime);

		const toBooking = (): void => {
			const payload = {
				id: showtime.id,
				poster,
				movie: name,
				time: showtime.time,
				date: showtime.date,
				age,
			};
			dispatch(setShowtime(payload));
			sendToCustomer({
				action: 'setShowtime',
				payload,
			});
			router.navigate('/pos/booking');
		};

		const onPress = () => {
			if (showtime.status) {
				if (currentDate !== showtime.date) {
					Alert.alert(
						'Chọn suất chiếu',
						'Bạn đang chọn suất chiếu ngày ' +
							showtime.date.replace('/' + currentY, ''),
						[
							{
								text: 'Chọn lại',
								style: 'cancel',
							},
							{
								text: 'Tiếp tục',
								onPress: toBooking,
							},
						],
					);
				} else {
					toBooking();
				}
			} else {
				showMessage({
					message: 'Suất chiếu đã ngừng bán',
				});
			}
		};

		let style: ViewStyle[] = [styles.showtime];
		let styleText: TextStyle[] = [styles.showtimeText];
		if (current && current.id === showtime.id) {
			style.push(styles.active);
			styleText.push(styles.textActive);
		}
		if (!showtime.status) {
			style.push(styles.disabled);
		}

		return (
			<TouchableOpacity onPress={onPress} style={style}>
				<Text style={styleText}>{showtime.time}</Text>
				<Text style={[styleText, styles.seats]}>{showtime.end}</Text>
			</TouchableOpacity>
		);
	},
	isEqual,
);

Showtime.displayName = 'Showtime';

const styles = StyleSheet.create({
	showtime: {
		flex: 1,
		maxWidth: 75,
		padding: 4,
		paddingHorizontal: 5,
		backgroundColor: primary,
		margin: 2,
		borderRadius: 10,
	},
	showtimeText: {
		fontSize: 16,
		lineHeight: 16,
		fontWeight: 'bold',
		textAlign: 'center',
		color: '#fff',
	},
	active: {
		backgroundColor: primary,
	},
	textActive: {
		color: '#fff',
	},
	disabled: {
		backgroundColor: '#bfbfbf',
	},
	seats: {
		fontSize: 13,
		fontWeight: 'normal',
		lineHeight: 14,
	},
});

export default Showtime;
