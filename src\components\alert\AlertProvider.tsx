import React, { useReducer } from 'react';
import {
	ActionType,
	AlertAction,
	AlertState,
	IAlertContext,
	ShowAlertProps,
} from './types';
import ModalAlert from './ModalAlert';

export const initialState: AlertState = {
	isReady: false,
	title: '',
	message: '',
	buttons: [],
	visible: false,
	icon: true,
	dispatch: (params?: any): void => {
		console.error('store is NOT ready', params);
	},
};

export const AlertContext = React.createContext<IAlertContext>({
	state: initialState,
	dispatch: () => null,
});

const alertReducer = (
	state: AlertState,
	action: AlertAction,
): typeof initialState => {
	switch (action.type) {
		case ActionType.SHOW:
			return {
				...state,
				...action.payload,
				visible: true,
			};
		case ActionType.HIDE:
			return {
				...state,
				visible: false,
			};
		default:
			return {
				...state,
			};
	}
};

export function showAlert({
	title = '',
	message = '',
	buttons = [],
}: ShowAlertProps) {
	initialState.dispatch({
		type: ActionType.SHOW,
		payload: {
			title: title,
			message: message,
			buttons: buttons,
		},
	});
}

export const AlertProvider: React.FC = React.memo(({ children }: any) => {
	const [state, dispatch] = useReducer(alertReducer, initialState);

	initialState.dispatch = (params: any) => {
		dispatch(params);
	};

	return (
		<AlertContext.Provider value={{ state, dispatch }}>
			{children}
			<ModalAlert />
		</AlertContext.Provider>
	);
});

AlertProvider.displayName = 'AlertProvider';

export default AlertProvider;
