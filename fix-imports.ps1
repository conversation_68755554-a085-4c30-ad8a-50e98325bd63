# Script to fix all import paths

Write-Host "Fixing all import paths..."

# Get all TypeScript and JavaScript files
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts", "*.tsx", "*.js", "*.jsx"

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content

    # Replace all old import paths with new ones
    $content = $content -replace '@/components/Alert', '@/components/alert'
    $content = $content -replace '@/components/Button', '@/components/button'
    $content = $content -replace '@/components/CinemaScreen', '@/components/cinema-screen'
    $content = $content -replace '@/components/FlatList', '@/components/flat-list'
    $content = $content -replace '@/components/Image', '@/components/image'
    $content = $content -replace '@/components/Input', '@/components/input'
    $content = $content -replace '@/components/Loading', '@/components/loading'
    $content = $content -replace '@/components/MemberCard', '@/components/member-card'
    $content = $content -replace '@/components/Menu', '@/components/menu'
    $content = $content -replace '@/components/Modal', '@/components/modal'
    $content = $content -replace '@/components/ScrollView', '@/components/scroll-view'
    $content = $content -replace '@/components/Topbar', '@/components/topbar'

    $content = $content -replace '@/screens/Booking', '@/screens/booking'
    $content = $content -replace '@/screens/Checkout', '@/screens/checkout'
    $content = $content -replace '@/screens/Exchange', '@/screens/exchange'
    $content = $content -replace '@/screens/Members', '@/screens/members'
    $content = $content -replace '@/screens/Print', '@/screens/print'
    $content = $content -replace '@/screens/Products', '@/screens/products'
    $content = $content -replace '@/screens/Reports', '@/screens/reports'
    $content = $content -replace '@/screens/Tickets', '@/screens/tickets'
    $content = $content -replace '@/screens/Timelines', '@/screens/timelines'
    $content = $content -replace '@/screens/Welcome', '@/screens/welcome'

    $content = $content -replace '@/store/App', '@/store/app'
    $content = $content -replace '@/store/Booking', '@/store/booking'
    $content = $content -replace '@/store/Cart', '@/store/cart'
    $content = $content -replace '@/store/Member', '@/store/member'
    $content = $content -replace '@/store/Product', '@/store/product'

    # Write back file if there are changes
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
        Write-Host "Fixed: $relativePath"
    }
}

Write-Host "Completed fixing import paths!"
