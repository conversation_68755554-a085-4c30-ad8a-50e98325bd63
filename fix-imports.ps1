# Script to fix all import paths - more comprehensive

Write-Host "Fixing all import paths comprehensively..."

# Get all TypeScript and JavaScript files
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts", "*.tsx", "*.js", "*.jsx"

$totalFixed = 0

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content

    # Replace all old import paths with new ones - using regex for more precise matching
    $content = $content -replace '@/components/Alert\b', '@/components/alert'
    $content = $content -replace '@/components/Button\b', '@/components/button'
    $content = $content -replace '@/components/CinemaScreen\b', '@/components/cinema-screen'
    $content = $content -replace '@/components/FlatList\b', '@/components/flat-list'
    $content = $content -replace '@/components/Image\b', '@/components/image'
    $content = $content -replace '@/components/Input\b', '@/components/input'
    $content = $content -replace '@/components/Loading\b', '@/components/loading'
    $content = $content -replace '@/components/MemberCard\b', '@/components/member-card'
    $content = $content -replace '@/components/Menu\b', '@/components/menu'
    $content = $content -replace '@/components/Modal\b', '@/components/modal'
    $content = $content -replace '@/components/ScrollView\b', '@/components/scroll-view'
    $content = $content -replace '@/components/Topbar\b', '@/components/topbar'

    $content = $content -replace '@/screens/Booking\b', '@/screens/booking'
    $content = $content -replace '@/screens/Checkout\b', '@/screens/checkout'
    $content = $content -replace '@/screens/Exchange\b', '@/screens/exchange'
    $content = $content -replace '@/screens/Members\b', '@/screens/members'
    $content = $content -replace '@/screens/Print\b', '@/screens/print'
    $content = $content -replace '@/screens/Products\b', '@/screens/products'
    $content = $content -replace '@/screens/Reports\b', '@/screens/reports'
    $content = $content -replace '@/screens/Tickets\b', '@/screens/tickets'
    $content = $content -replace '@/screens/Timelines\b', '@/screens/timelines'
    $content = $content -replace '@/screens/Welcome\b', '@/screens/welcome'

    $content = $content -replace '@/store/App\b', '@/store/app'
    $content = $content -replace '@/store/Booking\b', '@/store/booking'
    $content = $content -replace '@/store/Cart\b', '@/store/cart'
    $content = $content -replace '@/store/Member\b', '@/store/member'
    $content = $content -replace '@/store/Product\b', '@/store/product'

    # Write back file if there are changes
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
        Write-Host "Fixed: $relativePath"
        $totalFixed++
    }
}

Write-Host "Completed fixing import paths! Fixed $totalFixed files."
