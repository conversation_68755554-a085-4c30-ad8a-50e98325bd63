﻿import React, { useEffect } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useSelector } from 'react-redux';

import Sidebar from './Sidebar';
import Detail from './Detail';
import Product from './Product';
import Box from '@/components/Box';
import sendToCustomer from '@/services/Broadcast';
import { isCustomer } from '@/utils';
import { getMember, setMemberOrder } from '@/store/cart';
import { getOrder } from '@/store/cart/Selector';
import { useAppDispatch } from '@/store';
import primary from '@/constants/Colors';

const CheckoutScreen = () => {
	const dispatch = useAppDispatch();

	const order = useSelector(getOrder);
	const member = useSelector(getMember);

	useEffect(() => {
		if (member) {
			if (order.order_id > 0 && member.member_id && !isCustomer()) {
				requestAnimationFrame(() => {
					dispatch(
						setMemberOrder({
							order: order.order_id,
							member: member.member_id,
						}),
					);
				});
			}
		}
		if (order.order_id > 0) {
			requestAnimationFrame(() => {
				sendToCustomer({
					action: 'setCart',
					payload: order,
				});
			});
		}
	}, [order.order_id, member]); // eslint-disable-line react-hooks/exhaustive-deps

	return (
		<View style={styles.bg}>
			<View style={styles.header}>
				<Text style={styles.headerText}>Thanh toán</Text>
			</View>
			<View style={styles.row}>
				<View style={styles.col}>
					<Box>
						<Detail />
					</Box>
				</View>
				<View style={styles.col}>
					<Box>
						<Product />
					</Box>
				</View>
				<View style={styles.action}>
					<Sidebar orderId={order.order_id} amount={order.amount} />
				</View>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	header: {
		backgroundColor: primary,
		height: 50,
		alignItems: 'center',
		justifyContent: 'center',
		paddingRight: 320,
	},
	headerText: {
		fontSize: 20,
		color: '#fff',
		fontWeight: 'bold',
	},
	row: {
		flex: 1,
		flexDirection: 'row',
		paddingLeft: 10,
	},
	col: {
		flex: 1,
		marginHorizontal: 6,
		marginVertical: 20,
	},
	action: {
		width: 320,
		marginLeft: 10,
		marginTop: -40,
		flexDirection: 'row',
		boxShadow: '0 0 7px rgba(0, 0, 0, .3)',
		overflow: 'hidden',
	},
	shadow: {
		flex: 1,
		borderTopLeftRadius: 15,
	},
	borderStyle: {
		marginTop: 12,
	},
});

export default CheckoutScreen;
