import React from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';

import FlatList from '@/components/flatlist';
import { getData } from '@/store/member/Slice';

import Row from './Row';
import Empty from './Empty';
import styles from '../styles';
import type { IMemberDetail } from '@/types/Member';

const List = React.memo(() => {
	const data = useSelector(getData);

	const renderItem = ({ item }: { item: IMemberDetail }) => <Row data={item} />;

	return (
		<View style={styles.bg}>
			<View style={styles.list}>
				<View style={[styles.row]}>
					<View style={[styles.cell, styles.id, styles.header]}>
						<Text>M� KH</Text>
					</View>
					<View style={[styles.cell, styles.name, styles.header]}>
						<Text>H? t�n</Text>
					</View>
					<View style={[styles.cell, styles.phone, styles.header]}>
						<Text>S�T</Text>
					</View>
					<View style={[styles.cell, styles.birthday, styles.header]}>
						<Text><PERSON>�y sinh</Text>
					</View>
					<View style={[styles.cell, styles.identification, styles.header]}>
						<Text>CMND</Text>
					</View>
					<View style={[styles.cell, styles.point, styles.header]}>
						<Text>�.�?i thu?ng</Text>
					</View>
					<View style={[styles.cell, styles.point, styles.header]}>
						<Text>�.Thang h?ng</Text>
					</View>
					<View style={[styles.cell, styles.level, styles.header]}>
						<Text>C?p d?</Text>
					</View>
					<View
						style={[
							styles.cell,
							styles.action,
							styles.header,
							styles.actionHeader,
						]}
					/>
				</View>
				<FlatList
					data={data}
					keyExtractor={(item) => item.id}
					renderItem={renderItem}
					ListEmptyComponent={Empty}
					showsHorizontalScrollIndicator={true}
				/>
			</View>
		</View>
	);
});

List.displayName = 'MemberList';

export default List;
