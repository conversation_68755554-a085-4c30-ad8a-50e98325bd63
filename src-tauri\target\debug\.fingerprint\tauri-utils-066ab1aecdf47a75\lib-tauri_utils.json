{"rustc": 10895048813736897673, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 5607667501770804077, "deps": [[561782849581144631, "html5ever", false, 15022628028308121315], [1200537532907108615, "url<PERSON><PERSON>n", false, 12134397789359388026], [3060637413840920116, "proc_macro2", false, 13520058446541265646], [3150220818285335163, "url", false, 14317979308849973313], [3191507132440681679, "serde_untagged", false, 15943093652960529944], [4899080583175475170, "semver", false, 2426471561692137791], [5986029879202738730, "log", false, 6839761351586039372], [6213549728662707793, "serde_with", false, 4347718113671465745], [6262254372177975231, "kuchiki", false, 118831164400589629], [6606131838865521726, "ctor", false, 16158425920095406536], [6913375703034175521, "schemars", false, 17719869593701131168], [7170110829644101142, "json_patch", false, 3739604338288812391], [8319709847752024821, "uuid", false, 15916998633034192982], [9010263965687315507, "http", false, 14314634951049504858], [9451456094439810778, "regex", false, 5575854039059109283], [9689903380558560274, "serde", false, 17710615169291184661], [10806645703491011684, "thiserror", false, 11115062776651022174], [11655476559277113544, "cargo_metadata", false, 3844990948782409599], [11989259058781683633, "dunce", false, 8003502364352757275], [13625485746686963219, "anyhow", false, 12400837003654224066], [14132538657330703225, "brotli", false, 396834054790866260], [15367738274754116744, "serde_json", false, 4006064592906587583], [15609422047640926750, "toml", false, 980020049899024968], [15622660310229662834, "walkdir", false, 11897401627305349443], [15932120279885307830, "memchr", false, 16137764857162929760], [17146114186171651583, "infer", false, 13985710910049689603], [17155886227862585100, "glob", false, 10599412529121352411], [17186037756130803222, "phf", false, 18019884910998057306], [17990358020177143287, "quote", false, 1666506227630477606]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-066ab1aecdf47a75\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}