﻿import React, { useCallback } from 'react';
import { StyleSheet, Text } from 'react-native';
import { useSelector } from 'react-redux';
import { router } from 'expo-router';

import { reset } from '@/services/Booking';

import Alert from '@/components/alert';
import Topbar from '@/components/topbar';

import { useAppDispatch } from '@/store';
import { getSelected } from '@/store/booking';
import { currentShowtime, setSeats } from '@/store/booking/Slice';

import primary from '@/constants/Colors';
import { setMember } from '@/store/cart';

const HeaderBar = React.memo(() => {
	const dispatch = useAppDispatch();
	const current = useSelector(currentShowtime);
	const seats = useSelector(getSelected);

	const onCancel = useCallback(() => {
		if (seats.length > 0) {
			Alert.alert(
				'Quay lại',
				'Đang có ghế đang chọn, bọn c<PERSON> muốn giữ ghế hiện tại?',
				[
					{
						text: 'Không',
						onPress: backWithReset,
					},
					{
						text: 'Giữ ghế',
						onPress: () => {
							router.navigate('/pos/tickets');
						},
					},
				],
			);
		} else {
			dispatch(setMember(null));
			router.navigate('/pos/tickets');
		}
	}, [seats]); // eslint-disable-line react-hooks/exhaustive-deps

	const backWithReset = () => {
		reset();
		dispatch(setSeats([]));
		dispatch(setMember(null));
		router.navigate('/pos/tickets');
	};

	return (
		<Topbar text={current?.movie || 'Đặt vé'} goBack={onCancel}>
			{current && (
				<Text numberOfLines={1} style={styles.title}>
					Suất chiếu
					<Text style={styles.bold}> {current.time}</Text> ngày
					<Text style={styles.bold}> {current.date}</Text>
				</Text>
			)}
		</Topbar>
	);
});

HeaderBar.displayName = 'HeaderBar';

const styles = StyleSheet.create({
	bg: {
		backgroundColor: primary,
		paddingLeft: 15,
		height: 60,
		paddingRight: 250,
		alignItems: 'center',
		justifyContent: 'center',
	},
	movie: {
		color: '#fff',
		fontSize: 20,
		fontWeight: 'bold',
		textAlign: 'center',
	},
	title: {
		color: '#fff',
		textAlign: 'center',
		fontSize: 16,
	},
	bold: {
		fontWeight: 'bold',
	},
});

export default HeaderBar;
