{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 5811134107190303418], [10755362358622467486, "build_script_build", false, 7669751951434522486], [13890802266741835355, "build_script_build", false, 214681061352172037], [246920333930397414, "build_script_build", false, 11082327870655985951], [7236291379133587555, "build_script_build", false, 13498649585516520867], [17509843537913359226, "build_script_build", false, 5121754575466488397], [1582828171158827377, "build_script_build", false, 18186587240510745694], [11721252211900136025, "build_script_build", false, 14281955196853153044]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-dc6d078b42d2537e\\output", "paths": ["tauri.conf.json", "capabilities", "TwPrinter-x86_64-pc-windows-msvc.exe", "cashdrawer\\cashdrawer.exe", "cashdrawer\\cc3280.dll", "cashdrawer\\inpout32.dll", "logo-ticket.png"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}