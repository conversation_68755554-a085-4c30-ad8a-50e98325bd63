import { createAsyncThunk } from '@reduxjs/toolkit';
import { getShowtime as getShowtimeAPI } from '@/services/Booking';
import { setLoading } from '@/store/app';

export const getShowtime = createAsyncThunk(
	'booking/getShowtime',
	async (args: string, thunkApi) => {
		thunkApi.dispatch(setLoading(true));
		try {
			const response = await getShowtimeAPI(args);
			thunkApi.dispatch(setLoading(false));
			return response;
		} catch (e) {
			console.log(e);
		}
		thunkApi.dispatch(setLoading(false));
		return thunkApi.rejectWithValue('Error');
	},
);
