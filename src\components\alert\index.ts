import { Alert as NativeAlert, Platform } from 'react-native';

import { ActionType } from './types';
import { initialState } from './AlertProvider';
export { showAlert, AlertProvider } from './AlertProvider';

const alertPolyfill = {
	alert(title: string, message: string, buttons: any, extra?: any) {
		initialState.dispatch({
			type: ActionType.SHOW,
			payload: {
				title,
				message,
				buttons,
			},
		});
	},
};

const Alert = Platform.OS === 'web' ? alertPolyfill : NativeAlert;

export default Alert;
