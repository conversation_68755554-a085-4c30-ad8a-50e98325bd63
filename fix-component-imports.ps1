# Script to fix remaining component import paths

Write-Host "Fixing remaining component import paths..."

# Get all TypeScript and JavaScript files
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts", "*.tsx", "*.js", "*.jsx"

$totalFixed = 0

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content

    # Fix remaining import paths
    $content = $content -replace '@/components/flatlist', '@/components/FlatList'
    $content = $content -replace '@/components/scrollview', '@/components/ScrollView'
    $content = $content -replace '@/components/image', '@/components/Image'
    $content = $content -replace '@/components/input', '@/components/Input'

    # Write back file if there are changes
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
        Write-Host "Fixed: $relativePath"
        $totalFixed++
    }
}

Write-Host "Fixed $totalFixed files."
Write-Host "Remaining component import paths fixed!"
