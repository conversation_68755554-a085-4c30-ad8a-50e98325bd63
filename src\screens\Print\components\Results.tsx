﻿import React from 'react';
import { StyleSheet, View } from 'react-native';
import isEqual from 'react-fast-compare';

import Tickets from './Tickets';
import ListProducts from '@/screens/checkout/components/ListProducts';
import { ICartProduct, ICartTicket } from '@/types/Cart';

type Props = {
	data: {
		tickets: ICartTicket[];
		products: ICartProduct[];
	};
	type: string;
};

const Results = React.memo(({ data, type }: Props) => {
	return (
		<View style={styles.bg}>
			{data.tickets && data.tickets.length > 0 && (
				<Tickets data={data.tickets} type={type} />
			)}
			{data.products && data.products.length > 0 && (
				<ListProducts data={data.products} />
			)}
		</View>
	);
}, isEqual);

Results.displayName = 'PrintResults';

const styles = StyleSheet.create({
	bg: {
		backgroundColor: '#fff',
		flex: 1,
		marginHorizontal: 10,
	},
});

export default Results;
