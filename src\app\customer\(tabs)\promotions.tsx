﻿import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import Configs from '@/constants/App';
import { get as getAPI } from '@/services';
import { getConfigs } from '@/store/app';
import CinemaScreen from '@/components/cinema-screen';

const PromotionsScreen = () => {
	const config = useSelector(getConfigs);
	const [uri, setUri] = useState(Configs.menu);

	useEffect(() => {
		const getData = async () => {
			try {
				const response = await getAPI('/screen');
				if (response.url) {
					console.log(response.url);
					setUri(response.url);
				}
			} catch (e) {
				console.log(e);
			}
		};
		if (config.server.length > 0) {
			getData();
		}
	}, [config]);

	return <CinemaScreen uri={uri} />;
};

export default PromotionsScreen;
