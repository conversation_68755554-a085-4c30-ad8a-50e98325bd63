﻿import { memo, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useSelector } from 'react-redux';

import { getPrices } from '@/store/booking';
import Button from '@/components/button';

type Props = {
	onChange: (value: string) => void;
};

const ModalBulks = memo(({ onChange }: Props) => {
	const prices = useSelector(getPrices);

	const [price, setPrice] = useState<string>('');

	const changePrice = (value: string) => {
		setPrice(value);
	};

	const onSubmit = () => {
		if (price.length > 0) {
			onChange(price);
		}
	};

	return (
		<View style={styles.modalBody}>
			<Text style={styles.heading}>Đổi giá tất cả vé</Text>
			<Picker
				selectedValue={price}
				onValueChange={changePrice}
				style={styles.picker}>
				<Picker.Item label={'--Chọn giá vé--'} value={''} />
				{prices.map((item) => (
					<Picker.Item
						label={item.price_for_name}
						value={item.price_for_name}
						key={'modal' + item.id}
					/>
				))}
			</Picker>
			<Button onPress={onSubmit} style={styles.button}>
				<Text style={styles.label}>Đổi giá vé</Text>
			</Button>
		</View>
	);
});

ModalBulks.displayName = 'BookingSidebarBulks';

const styles = StyleSheet.create({
	modalBody: {
		backgroundColor: '#fff',
		paddingHorizontal: 40,
		paddingVertical: 10,
		paddingBottom: 20,
		width: 300,
		alignItems: 'center',
		borderRadius: 15,
	},
	heading: {
		fontSize: 20,
		paddingBottom: 20,
	},
	picker: {
		height: 32,
		borderWidth: 0,
		borderColor: 'transparent',
		fontSize: 14,
		backgroundColor: '#f0f0f0',
	},
	button: {
		marginTop: 20,
	},
	label: {
		color: '#fff',
	},
});

export default ModalBulks;
