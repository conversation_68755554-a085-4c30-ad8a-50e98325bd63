import React, { useState, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';

import { getSelected } from '@/store/booking/Selector';
import { getMaps } from '@/store/booking/Slice';
import SeatLabel from '../SeatLabel';
import Seat from '../Seat';
import type { IBookingSeat } from '@/types/Booking';

const Seats = React.memo(({ viewWidth }: { viewWidth: number }) => {
	const [labels, setLabels] = useState([]);
	const [maxWidth, setMaxWidth] = useState(1);
	const [maxHeight, setMaxHeight] = useState(1);

	const data = useSelector(getMaps);

	const selected = useSelector(getSelected);

	useEffect(() => {
		if (data.length > 0) {
			let tmp: any = {};
			let width = 0;
			data.forEach((item: IBookingSeat) => {
				let name = item.name.replace(/\d/g, '');
				if (width === 0) {
					width = parseInt(item.w);
				}
				if (typeof tmp[name] === 'undefined') {
					tmp[name] = {
						label: name,
						width: width,
						height: parseInt(item.h),
						top: parseInt(item.y),
					};
				}
			});
			tmp = Object.keys(tmp).map((k) => tmp[k]);
			if (JSON.stringify(tmp) !== JSON.stringify(labels)) {
				setLabels(tmp);
			}
		}
	}, [data, labels]);

	useEffect(() => {
		if (data.length > 0) {
			const w = Math.max(...data.map((seat: IBookingSeat) => parseInt(seat.x)));
			const h = Math.max(...data.map((seat: IBookingSeat) => parseInt(seat.y)));
			const width = w + parseInt(data.slice(0, 1)[0].w);
			const height = h + parseInt(data.slice(0, 1)[0].h);
			setMaxWidth(width);
			setMaxHeight(height);
		}
	}, [data]);

	const scale = viewWidth / maxWidth;
	const transX = (maxWidth - viewWidth) / 2 / scale;
	const transY = 0;

	return (
		<View
			style={[
				styles.maps,
				{
					width: maxWidth,
					height: maxHeight,
					transform: [
						{ scale: scale },
						{ translateX: -transX },
						{ translateY: -transY },
					],
				},
			]}>
			{data.map((item: IBookingSeat) => (
				<Seat
					id={item.id}
					isSelected={selected.some(
						(seat: IBookingSeat) => seat.id === item.id,
					)}
					key={'seat-' + item.id}
				/>
			))}
			<SeatLabel data={labels} scale={scale} />
		</View>
	);
});

Seats.displayName = 'MapSeats';

const styles = StyleSheet.create({
	maps: {
		position: 'relative',
		flex: 1,
		marginTop: 20,
	},
});

export default Seats;
