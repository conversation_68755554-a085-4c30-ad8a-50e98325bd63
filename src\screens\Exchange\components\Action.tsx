﻿import React, { memo, useCallback, useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import isEqual from 'react-fast-compare';
import { router } from 'expo-router';

import Button from '@/components/button';
import { getMember, setGifts, setMember } from '@/store/cart/Slice';
import { showMessage } from '@/utils/Toast';
import { claim } from '@/services/Member';
import { currencyFormat, getDateTime, isCustomer } from '@/utils';
import sendToCustomer from '@/services/Broadcast';
import { RootState, useAppDispatch } from '@/store';
import { getConfigs, getUser, setLoading } from '@/store/app';
import printTask from '@/utils/printer';

const Action = memo(() => {
	const dispatch = useAppDispatch();

	const current = useSelector((state: RootState) => state.cart.usedPoint);
	const configs = useSelector(getConfigs);
	const user = useSelector(getUser);
	const gifts = useSelector((state: RootState) => state.cart.gifts);
	const member = useSelector(getMember);

	const pointLeft = member ? member.point_left - current : 0;

	useEffect(() => {
		sendToCustomer({
			action: 'setGifts',
			payload: {
				gifts,
				usedPoint: current,
			},
		});
	}, [gifts, current]);

	const onCheckout = async () => {
		if (member && current > 0) {
			const tasks = [];
			let totalPoint = 0;

			dispatch(setLoading(true));

			for (const item of gifts) {
				try {
					await claim({
						member_id: member.member_id,
						id: item.id,
						quantity: item.quantity,
					});
					totalPoint += item.point * item.quantity;
					tasks.push(item);
				} catch (e) {
					console.log(e);
				}
			}

			if (tasks.length === 0) {
				dispatch(setLoading(false));
				showMessage({
					message: 'Đổi điểm không thành công',
					type: 'danger',
				});
				return;
			}

			await printTask(
				{
					detail: {
						seller: user.name,
						counter: configs.counter,
						time: getDateTime(),
						member: member.member_id,
					},
					products: tasks.map((item) => {
						return [
							item.name,
							item.quantity,
							item.point,
							item.point * item.quantity,
						];
					}),
					point: {
						used: currencyFormat(totalPoint),
						left: currencyFormat(member.point_left - totalPoint),
					},
				},
				configs.printer,
				'point',
			);

			dispatch(setLoading(false));

			showMessage({
				message: 'Đổi điểm thành công',
				type: 'success',
			});

			dispatch(setGifts([]));
			dispatch(
				setMember({
					...member,
					point_left: member.point_left - totalPoint,
				}),
			);
		} else {
			showMessage({
				message: 'Bạn chưa đổi điểm',
				type: 'warning',
			});
		}
	};

	const onCancel = useCallback(() => {
		dispatch(setGifts([]));
		router.back();
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	if (member) {
		return (
			<View style={styles.bg}>
				<View style={styles.attr}>
					<Text style={styles.text}>Điểm hiện có:</Text>
					<Text style={styles.value}>{member.point_left}</Text>
				</View>
				<View style={styles.attr}>
					<Text style={styles.text}>Điểm đang dùng:</Text>
					<Text style={styles.value}>{current}</Text>
				</View>
				<View style={styles.attr}>
					<Text style={styles.text}>Điểm còn lại:</Text>
					<Text style={styles.value}>{pointLeft}</Text>
				</View>
				{!isCustomer() && (
					<View>
						<View style={styles.row}>
							<Button
								onPress={onCancel}
								style={styles.btn}
								text="Quay lại"
								textStyle={styles.btnText}
							/>
							<Button
								onPress={onCheckout}
								style={styles.btn}
								text="Xác nhận"
								textStyle={styles.btnText}
							/>
						</View>
					</View>
				)}
			</View>
		);
	}
	return null;
}, isEqual);

Action.displayName = 'ExchangeAction';

const styles = StyleSheet.create({
	bg: {
		borderColor: '#eee',
		borderTopWidth: 1,
		paddingVertical: 5,
	},
	btn: {
		flex: 1,
		paddingHorizontal: 10,
		margin: 8,
		marginHorizontal: 8,
	},
	btnText: {
		color: '#fff',
		textAlign: 'center',
		fontSize: 20,
		textTransform: 'uppercase',
	},
	row: {
		flexDirection: 'row',
	},
	attr: {
		marginHorizontal: 10,
		flexDirection: 'row',
	},
	text: {
		flex: 1,
		fontSize: 20,
	},
	value: {
		alignSelf: 'flex-end',
		fontWeight: 'bold',
		fontSize: 20,
	},
});

export default Action;
