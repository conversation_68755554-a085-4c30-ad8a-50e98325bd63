["\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\E:\\touchcinema\\touchcinema-pos\\src-tauri\\target\\debug\\build\\tauri-0c5cd426b8903fc1\\out\\permissions\\menu\\autogenerated\\default.toml"]