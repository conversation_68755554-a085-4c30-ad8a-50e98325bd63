import React from 'react';
import {
	Platform,
	Text,
	TouchableOpacity,
	View,
	Modal as RNModal,
	ModalProps,
	ViewStyle,
	Pressable,
} from 'react-native';
import Loading from '@/components/loading';
import styles from './styles';
import Toast from '@/utils/Toast';

type CustomProps = {
	closeBtn?: boolean;
	onBackdropPress?: () => void;
	style?: ViewStyle;
	children?: React.ReactNode;
};

type Props = ModalProps & CustomProps;

const Modal = React.memo((props: Props) => {
	const onBackdropPress = (e: any) => {
		if (typeof props.onBackdropPress === 'function') {
			props.onBackdropPress();
		} else if (props.onRequestClose) {
			props.onRequestClose(e);
		}
	};

	if (Platform.OS === 'windows') {
		return null;
	} else {
		const visibleBtn: boolean =
			typeof props.closeBtn === 'undefined' || props.closeBtn !== false;
		return (
			<RNModal {...props} transparent style={styles.bg}>
				<Pressable
					style={styles.backdrop}
					onPress={onBackdropPress}></Pressable>
				<View style={styles.bg}>
					<View style={[styles.body, props.style]}>
						{props?.children}
						{visibleBtn && (
							<TouchableOpacity
								onPress={props.onRequestClose}
								style={styles.close}>
								<Text style={styles.closeIcon}>&#10005;</Text>
							</TouchableOpacity>
						)}
					</View>
					<Loading />
					<Toast />
				</View>
			</RNModal>
		);
	}
});

Modal.displayName = 'Modal';

export default Modal;
