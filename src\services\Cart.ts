﻿import { get, post } from '@/services';
import { CreateOrderParams } from '@/store/cart';

const createOrder = async (data: CreateOrderParams) => {
	const response = await post('/cinema/book', data);
	return {
		tickets: response.data.seats,
		products: response.data.combos,
		amount: response.data.price,
		order_id: response.data.order_id,
		bill: response.data.bill,
		payment_methods: response.data.payment_methods,
	};
};

export const postCheckout = async (data: any) => {
	const response = await post('/cinema/checkout', data);
	return { ...response, products: response.fastfoods };
};

type PostVoucherType = {
	code: string;
	seri?: string;
	orderId: number;
};
export const postVoucher = async ({ code, orderId, seri }: PostVoucherType) => {
	const response = await post('/cinema/voucher', {
		seri,
		code,
		order_id: orderId,
	});
	return response;
};

export const cancelOrder = async (orderId: number) => {
	const response = await post('cinema/checkout', {
		order_id: orderId,
		payment: 3,
	});
	return response;
};

export const getOrder = async (order_id: number, notification: boolean) => {
	try {
		const response = await get(
			'/cinema/search?order_id=' + order_id,
			notification,
		);
		if (response && response.data.constructor === Array) {
			return {
				tickets: response.data,
				products: response.combo || [],
				bill: response.bill || '',
				amount: response.amount,
			};
		}
	} catch (e) {
		console.log(e);
	}
	return false;
};

export const printExchange = async (data: any, member_id: string) => {
	const results = [];
	for (const item of data) {
		try {
			await post('/member/gifts', {
				member_id,
				id: item.id,
				quantity: item.quantity,
			});
			results.push({
				name: item.name,
				quantity: item.quantity,
				total: parseInt(item.quantity) * parseInt(item.point),
				point: item.point,
			});
		} catch (e) {
			console.log(e);
		}
	}
	return results;
};

export const setMember = async (data: any) => {
	const response = await post('/cinema/member', {
		member_id: data.member,
		order_id: data.order,
	});
	return response.order.point;
};

export default {
	createOrder,
	setMember,
};
