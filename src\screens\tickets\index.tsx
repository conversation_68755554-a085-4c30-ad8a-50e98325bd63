import React, { useEffect, useState } from 'react';
import {
	ActivityIndicator,
	StyleSheet,
	Text,
	View,
	LayoutChangeEvent,
	TouchableOpacity,
	InteractionManager,
} from 'react-native';
import { TabView } from 'react-native-tab-view';
import { useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';

import primary from '@/constants/Colors';
import { getSchedules } from '@/services/Booking';
import sendToCustomer, { channel } from '@/services/Broadcast';
import { getTimelines, setTimelines } from '@/store/Booking';
import { isCustomer } from '@/utils';
import { showMessage } from '@/utils/Toast';
import { useAppDispatch } from '@/store';

import { ILabel, ITimeline, Route } from './types';
import Tab from './components/Tab';

const TicketsScreen = () => {
	const dispatch = useAppDispatch();

	const isFocused = useIsFocused();

	const data = useSelector(getTimelines);

	const [active, setActive] = useState(0);
	const [viewWitdh, setViewWidth] = useState<number>(0);
	const [ready, setReady] = useState(false);

	const tabs = data.map((item: ITimeline, index: number): Route => {
		let date: string = '',
			day: string = '',
			month: string = '';
		const parse = item.date.split(',');
		day = parse[0];
		if (parse[1]) {
			const parseMonth = parse[1].split('/');
			month = parseMonth[1];
			date = parseMonth[0];
		}
		return {
			key: index.toString(),
			title: date,
			day: day,
			month: month,
		};
	});

	useEffect(() => {
		channel.addEventListener('message', (e: any) => {
			if (typeof e.data.cinema !== 'undefined') {
				setActive(e.data.cinema);
			}
		});

		return () => {
			channel.removeEventListener('message', (e: any) => {
				console.log(e);
			});
		};
	}, []);

	useEffect(() => {
		getData();
		const interactionPromise = InteractionManager.runAfterInteractions(() =>
			setReady(true),
		);
		return () => interactionPromise.cancel();
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	useEffect(() => {
		sendToCustomer({ cinema: active });
		if (!isCustomer()) {
			if (active > 0 && isFocused) {
				showMessage({
					message: `Bạn đang chọn lịch chiếu ngày ${tabs[active].day}`,
					type: 'warning',
					bottom: 20,
					duration: 1500,
				});
			}
		}
	}, [active, isFocused, tabs]);

	const getData = () => {
		requestAnimationFrame(async () => {
			try {
				const res = await getSchedules();
				if (res && res.length > 0) {
					dispatch(setTimelines(res));
				}
			} catch (e) {
				console.log(e);
			}
		});
	};

	const renderScene = ({ route }: { route: Route }) => {
		return <Tab index={route.key} data={data[parseInt(route.key)]} />;
	};

	const renderTabBar = (props: any) => {
		return (
			<View style={styles.tabBar}>
				{props.navigationState.routes.map((route: Route, i: number) => {
					const focused: boolean = i === active;
					return (
						<TouchableOpacity
							key={'tab-' + i}
							style={[
								styles.tabItem,
								focused ? styles.tabActive : styles.tabInActive,
							]}
							onPress={() => setActive(i)}>
							{renderLabel({ route, focused })}
						</TouchableOpacity>
					);
				})}
			</View>
		);
	};

	const renderLabel = ({ route, focused }: ILabel) => (
		<View style={[styles.tabItemInner]}>
			<Text style={[styles.tabLabel, focused ? styles.labelActive : null]}>
				{route.day}
			</Text>
			<Text
				style={[
					styles.tabLabel,
					styles.tabDate,
					focused ? styles.labelActive : null,
				]}>
				{route.title}/{route.month}
			</Text>
		</View>
	);

	const onLayout = (event: LayoutChangeEvent): void => {
		const { width } = event.nativeEvent.layout;
		setViewWidth(width);
	};

	return (
		<View style={styles.bg} onLayout={onLayout}>
			{ready === false && (
				<View style={styles.loading}>
					<View>
						<ActivityIndicator
							size="large"
							color={primary}
							style={styles.loading}
						/>
						<Text>Đang tải dữ liệu</Text>
					</View>
				</View>
			)}
			{ready === true && viewWitdh > 0 && data.length > 0 && (
				<TabView
					lazy
					lazyPreloadDistance={3}
					navigationState={{ index: active, routes: tabs }}
					renderScene={renderScene}
					renderTabBar={renderTabBar}
					onIndexChange={setActive}
					swipeEnabled={false}
					style={styles.wrap}
					initialLayout={{ width: viewWitdh }}
				/>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	wrap: {
		flex: 1,
		position: 'relative',
	},
	header: {
		flexDirection: 'row',
		justifyContent: 'center',
		marginBottom: 5,
	},
	tabBar: {
		paddingHorizontal: 10,
		flexDirection: 'row',
	},
	tabItem: {
		flex: 1,
		marginHorizontal: 3,
		backgroundColor: '#fff',
		padding: 8,
		borderRadius: 8,
	},
	tabInActive: {},
	tabActive: {
		backgroundColor: primary,
	},
	indicatorStyle: {
		height: 3,
		backgroundColor: primary,
	},
	tabItemInner: {
		alignItems: 'center',
	},
	tabLabel: {
		fontSize: 16,
		lineHeight: 16,
		color: '#000',
	},
	tabDate: {
		fontSize: 18,
		lineHeight: 18,
		fontWeight: 'bold',
		color: '#333',
	},
	labelActive: {
		color: '#fff',
	},
	loading: {
		alignItems: 'center',
		paddingTop: '15%',
	},
});

export default TicketsScreen;
