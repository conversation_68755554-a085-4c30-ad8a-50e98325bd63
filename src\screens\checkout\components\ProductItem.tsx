import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import isEqual from 'react-fast-compare';

import { ICartProduct } from '@/types/Cart';
import { currencyFormat } from '@/utils';

type Props = {
	item: ICartProduct;
};
const ProductItem = React.memo(({ item }: Props) => {
	return (
		<View style={styles.item}>
			<Text style={styles.name}>
				<Text style={styles.quantity}>x{item.quantity}</Text> {item.name}
			</Text>
			{item.price ? (
				<Text style={styles.price}>{currencyFormat(item.price)} đ</Text>
			) : (
				<Text style={styles.price}>{item.point} điểm</Text>
			)}
		</View>
	);
}, isEqual);

ProductItem.displayName = 'ProductItem';

const styles = StyleSheet.create({
	item: {
		marginVertical: 5,
	},
	name: {
		fontSize: 16,
	},
	quantity: {
		color: 'red',
		fontSize: 13,
	},
	price: {
		color: 'green',
	},
});

export default ProductItem;
