import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import isEqual from 'react-fast-compare';

import Separator from '@/components/Separator';
import { IReportItem } from '@/types/Report';

type ResultsProps = {
	overview: IReportItem[];
	categories: IReportItem[];
	total: number;
};

const Results = React.memo(({ overview, categories, total }: ResultsProps) => {
	const renderItem = ({ item }: { item: IReportItem }) => (
		<View style={styles.row}>
			<View style={styles.name}>
				<Text>{item.name}</Text>
			</View>
			<View style={styles.quantity}>
				<Text>{item.total}</Text>
			</View>
		</View>
	);
	return (
		<View style={styles.bg}>
			<View>
				<Text style={styles.title}>Foods & Drinks Overview</Text>
				<View style={styles.row}>
					<View style={styles.name}>
						<Text>Hàng</Text>
					</View>
					<View style={styles.quantity}>
						<Text>Số lượng</Text>
					</View>
				</View>
				<Separator />
				<FlatList
					data={overview}
					keyExtractor={(_, index) => 'o-' + index}
					renderItem={renderItem}
					ItemSeparatorComponent={Separator}
				/>
				<Text style={[styles.title, styles.header]}>Danh sách hàng</Text>
				<FlatList
					data={categories}
					keyExtractor={(_, index) => 'o-' + index}
					renderItem={renderItem}
					ItemSeparatorComponent={Separator}
				/>
				<Separator />
				<View style={styles.row}>
					<View style={styles.name}>
						<Text>Tổng</Text>
					</View>
					<View style={styles.quantity}>
						<Text>{total}</Text>
					</View>
				</View>
			</View>
		</View>
	);
}, isEqual);

Results.displayName = 'ReportResults';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		backgroundColor: '#fff',
		marginLeft: 20,
		padding: 20,
		paddingVertical: 10,
		borderRadius: 5,
	},
	title: {
		fontSize: 16,
		fontWeight: 'bold',
	},
	header: {
		paddingTop: 30,
	},
	row: {
		flexDirection: 'row',
		marginVertical: 3,
	},
	name: {
		flex: 1,
	},
	quantity: {
		width: 100,
		alignItems: 'center',
	},
});
export default Results;
