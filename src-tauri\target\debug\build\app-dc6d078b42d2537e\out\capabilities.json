{"default": {"identifier": "default", "description": "enables the default permissions", "local": true, "windows": ["main"], "permissions": ["core:default", "shell:default"]}, "desktop-capability": {"identifier": "desktop-capability", "description": "", "local": true, "windows": ["main", "customer"], "permissions": ["updater:default", "process:default", "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-unregister", "core:webview:allow-create-webview", "core:window:allow-create", "core:window:allow-available-monitors", "core:window:allow-set-fullscreen", "shell:allow-open", {"identifier": "shell:allow-execute", "allow": [{"args": true, "name": "TwPrinter", "sidecar": true}, {"args": ["Get-Printer | ConvertTo-Json"], "cmd": "powershell", "name": "get-printers", "sidecar": false}]}, {"identifier": "fs:allow-write-text-file", "allow": [{"path": "$TEMP/**"}]}], "platforms": ["macOS", "windows", "linux"]}}