import Icon from '@expo/vector-icons/Ionicons';

import MenuItem from './MenuItem';
import Alert from '@/components/Alert';
import { exitApp } from '@/modules/pos';

const ButtonExit = () => {
	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon size={size} name="power-outline" color={color} />
	);
	const onPress = () => {
		Alert.alert('Exit', 'Bạn có chắc muốn thoát ứng dụng?', [
			{
				text: 'Hủy',
				style: 'cancel',
			},
			{ text: 'Thoát', onPress: exitApp },
		]);
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Thoát'}
			isFocused={false}
		/>
	);
};

export default ButtonExit;
