import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import { IProductGroup } from '@/types/Product';

const productsSelector = (state: RootState) => state.products;

export const getProducts = createSelector(
	productsSelector,
	(products) => products.products,
);

export const getGroup = createSelector(
	productsSelector,
	(products) => products.group,
);

export const getGroupData = (id: number) => {
	return createSelector(productsSelector, (products) =>
		products.group.find((item: IProductGroup) => item.id === id),
	);
};

export const getGifts = createSelector(
	productsSelector,
	(products) => products.gifts,
);

export const getCombo = createSelector(
	productsSelector,
	(products) => products.combo,
);
