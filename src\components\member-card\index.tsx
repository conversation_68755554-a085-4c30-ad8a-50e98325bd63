﻿import React from 'react';
import { View, StyleSheet } from 'react-native';
import isEqual from 'react-fast-compare';
import { useSelector } from 'react-redux';

import { getMember } from '@/store/cart/Slice';
import Card from './Card';

const MemberCard = React.memo(() => {
	const member = useSelector(getMember);
	if (member && member.member_id) {
		return (
			<View style={styles.card}>
				<Card
					level={member.level}
					id={member.member_id}
					name={member.name}
					pointLevel={member.point_level}
					pointReward={member.point_left}
					point={member.point || 0}
					width={290}
				/>
			</View>
		);
	}
	return null;
}, isEqual);

MemberCard.displayName = 'MemberCard';

const styles = StyleSheet.create({
	card: {},
});

export default MemberCard;
