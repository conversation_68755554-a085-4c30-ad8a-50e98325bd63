import { get } from '@/services';
import { QrCodeProps } from '@/store/cart';

export const withQrCode = async ({ orderId, amount, gateway }: QrCodeProps) => {
	const response = await get(
		`https://kiosk.touchcinema.com/pos/${gateway}/${orderId}?amount=${amount}`,
	);
	if (response.qrcode) {
		return response.qrcode;
	}
	return false;
};

export const queryQrOrder = async (
	orderId: number,
	gateway: string,
	amount: number = 0,
) => {
	const response = await get(
		`https://kiosk.touchcinema.com/pos/${gateway}/query/${orderId}?amount=${amount}`,
	);
	return response;
};
