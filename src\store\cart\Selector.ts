import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/store';

const cart = (state: RootState) => state.cart;

export const getProducts = createSelector(cart, (state) => state.products);

export const getTickets = createSelector(cart, (state) => state.tickets);

export const getQrcode = createSelector(cart, (state) => state.qrcode);

export const getOrder = createSelector(cart, (state) => {
	return {
		order_id: state.order_id,
		bill: state.bill,
		amount: state.amount,
		products: state.products,
		tickets: state.tickets,
		payment: state.payment,
	};
});

export const getPayment = createSelector(cart, (state) => {
	return state.payment;
});
