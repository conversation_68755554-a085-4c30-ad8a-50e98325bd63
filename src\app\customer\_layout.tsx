﻿import { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { router, Stack } from 'expo-router';

import { channel, IPayload } from '@/services/Broadcast';
import { useAppDispatch } from '@/store';
import {
	clearOrder,
	setAmount,
	setCart,
	setGifts,
	setMember,
	setMoney,
	setOrderPoint,
	setPaymentMethod,
	setPaymentMethods,
	setQrcode,
} from '@/store/cart';
import { setModalSuccess, setToken } from '@/store/app';
import { setSeats, setShowtime } from '@/store/booking';
import { setProducts } from '@/store/product';
import Welcome from '@/screens/welcome';
import TabRoute from '@/types/Route';

export default function CustomerLayout() {
	const dispatch = useAppDispatch();

	useEffect(() => {
		channel.addEventListener('message', (e: any) => {
			if (e.data.action) {
				handleEvent(e.data);
			} else if (typeof e.data.route != 'undefined') {
				handleNavigation(e.data);
			}
		});

		return () => {
			channel.removeEventListener('message', (e: any) => {
				console.log(e);
			});
		};
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	const handleEvent = (data: IPayload): void => {
		switch (data.action) {
			case 'setCart':
				dispatch(setCart(data.payload));
				break;
			case 'setMember':
				dispatch(setMember(data.payload));
				break;
			case 'setMoney':
				dispatch(setMoney(data.payload));
				break;
			case 'setAmount':
				dispatch(setAmount(data.payload));
				break;
			case 'setPaymentMethods':
				dispatch(setPaymentMethods(data.payload));
				break;
			case 'setPayment':
				dispatch(setPaymentMethod(data.payload));
				break;
			case 'setOrderPoint':
				dispatch(setOrderPoint(data.payload));
				break;
			case 'setGifts':
				dispatch(setGifts(data.payload));
				break;
			case 'setQrcode':
				dispatch(setQrcode(data.payload));
				break;
			case 'clearOrder':
				dispatch(setQrcode(null));
				dispatch(clearOrder());
				break;
			case 'showSuccess':
				dispatch(setQrcode(null));
				dispatch(setModalSuccess(true));
				break;
			case 'setToken':
				dispatch(setToken(data.payload));
				break;
			case 'setShowtime':
				dispatch(setShowtime(data.payload));
				break;
			case 'setProducts':
				dispatch(setSeats(data.payload.seats));
				dispatch(setProducts(data.payload.products));
				break;
		}
	};

	const handleNavigation = (data: { route: string }): void => {
		switch (data.route) {
			case '':
				router.navigate('/customer/promotions');
				break;
			case '/booking-products':
			case '/products':
				router.navigate('/customer/products');
				break;
			case '/tickets':
			case '/booking':
			case '/checkout':
			case '/exchange':
			case '/members/create':
			case '/members/edit':
				const route = ('/customer/stack' + data.route) as TabRoute;
				router.navigate(route);
				break;
			default:
				router.navigate('/customer/promotions');
		}
	};

	return (
		<View style={styles.bg}>
			<Stack
				screenOptions={{
					headerShown: false,
				}}
			/>
			<Welcome />
		</View>
	);
}

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
	},
});
