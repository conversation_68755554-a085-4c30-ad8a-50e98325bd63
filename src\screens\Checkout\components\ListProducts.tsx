﻿import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import isEqual from 'react-fast-compare';

import Separator from '@/components/Separator';
import FlatList from '@/components/flat-list';
import { ICartProduct } from '@/types/Cart';
import ProductItem from './ProductItem';

type Props = {
	data: ICartProduct[];
};

const ListProducts = React.memo(({ data }: Props) => {
	const renderItem = ({ item }: { item: ICartProduct }) => (
		<ProductItem item={item} />
	);
	return (
		<View style={styles.wrap}>
			<View style={styles.header}>
				<Text style={styles.title}>BẮP NƯỚC ({data.length})</Text>
			</View>
			<View style={styles.list}>
				<FlatList
					data={data}
					keyExtractor={(_, index) => 'product-' + index}
					renderItem={renderItem}
					ItemSeparatorComponent={Separator}
				/>
			</View>
		</View>
	);
}, isEqual);

ListProducts.displayName = 'ListProducts';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
	},
	header: {
		backgroundColor: '#ddd',
		paddingHorizontal: 15,
		paddingVertical: 4,
	},
	title: {
		fontSize: 17,
	},
	list: {
		flex: 1,
		paddingHorizontal: 15,
		backgroundColor: '#fff',
	},
});

export default ListProducts;
