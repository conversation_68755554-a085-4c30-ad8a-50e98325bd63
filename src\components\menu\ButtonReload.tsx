import Icon from '@expo/vector-icons/Ionicons';

import MenuItem from './MenuItem';
import { reLoad } from '@/modules/pos';

const ButtonReload = () => {
	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon size={size} name="reload-outline" color={color} />
	);

	const onPress = () => {
		reLoad();
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Tải lại'}
			isFocused={false}
		/>
	);
};

export default ButtonReload;
