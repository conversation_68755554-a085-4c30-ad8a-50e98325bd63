import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import isEqual from 'react-fast-compare';

import { currencyFormat } from '@/utils';
import Separator from '@/components/Separator';
import { IReportDetail, TotalAmount } from '@/types/Report';

type DetailProps = {
	detail: IReportDetail;
	total: TotalAmount;
};

const Detail = React.memo(({ detail, total }: DetailProps) => {
	if (detail && detail.amount) {
		return (
			<View style={styles.bg}>
				<View style={styles.row}>
					<View>
						<Text>Tổng tiền bán:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.amount_sell)} đ
						</Text>
					</View>
				</View>
				<Separator />
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>Tiền mặt:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.amount_cash)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>Thẻ:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.amount_card)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>MoMo:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.momo)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>VNPay:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.vnpay)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>ShopeePay:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.shopeepay)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>Grab:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.grab || 0)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>Beamin:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.beamin || 0)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>Tổng tiền vé ({total.tickets}):</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.amount_ticket)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>
							Tổng Foods&Drinks ({total.products}):
						</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.amount_combo)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>Tổng tiền hủy:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.amount_total_cancel)} đ
						</Text>
					</View>
				</View>
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>
							Tổng tiền voucher ({detail.amount.voucher_count}):
						</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.amount_voucher)} đ
						</Text>
					</View>
				</View>
				<Separator />
				<View style={styles.row}>
					<View>
						<Text style={styles.label}>Tổng doanh thu:</Text>
					</View>
					<View style={styles.amount}>
						<Text style={styles.amountLabel}>
							{currencyFormat(detail.amount.amount_total)} đ
						</Text>
					</View>
				</View>
			</View>
		);
	}
	return null;
}, isEqual);

Detail.displayName = 'ReportDetail';

const styles = StyleSheet.create({
	bg: {
		paddingTop: 5,
		paddingBottom: 10,
	},
	row: {
		flexDirection: 'row',
		marginVertical: 4,
	},
	label: {
		fontSize: 16,
	},
	amount: {
		flex: 1,
		alignItems: 'flex-end',
	},
	amountLabel: {
		fontSize: 17,
		fontWeight: 'bold',
	},
});

export default Detail;
