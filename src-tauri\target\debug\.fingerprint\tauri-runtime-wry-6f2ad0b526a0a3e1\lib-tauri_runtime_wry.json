{"rustc": 10895048813736897673, "features": "[\"unstable\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 13416399214450221167, "deps": [[376837177317575824, "softbuffer", false, 12716869230230105750], [442785307232013896, "tauri_runtime", false, 5542784074252618259], [3150220818285335163, "url", false, 15668146286072074414], [3722963349756955755, "once_cell", false, 5493430054469386965], [4143744114649553716, "raw_window_handle", false, 10079824973013050502], [5986029879202738730, "log", false, 14669300167346157817], [7752760652095876438, "build_script_build", false, 4707812909874105366], [8539587424388551196, "webview2_com", false, 11566940673542702221], [9010263965687315507, "http", false, 14314634951049504858], [11050281405049894993, "tauri_utils", false, 3638413504225201697], [13223659721939363523, "tao", false, 7637019212109164584], [14585479307175734061, "windows", false, 15772416081231344459], [14794439852947137341, "wry", false, 14273566709688790158]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-6f2ad0b526a0a3e1\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}