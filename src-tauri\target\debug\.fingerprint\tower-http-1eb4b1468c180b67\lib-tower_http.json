{"rustc": 10895048813736897673, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 6070791047131720741, "deps": [[784494742817713399, "tower_service", false, 16934329087993414354], [1906322745568073236, "pin_project_lite", false, 2267887703063731787], [4121350475192885151, "iri_string", false, 8518840741041434508], [5695049318159433696, "tower", false, 6820591683597370819], [7712452662827335977, "tower_layer", false, 11588282847893017373], [7896293946984509699, "bitflags", false, 8340735141829365139], [9010263965687315507, "http", false, 14314634951049504858], [10629569228670356391, "futures_util", false, 5778955098311341807], [14084095096285906100, "http_body", false, 693391310947183790], [16066129441945555748, "bytes", false, 16001019836237442503]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-1eb4b1468c180b67\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}