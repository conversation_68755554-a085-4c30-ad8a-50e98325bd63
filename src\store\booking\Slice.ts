import { createSlice } from '@reduxjs/toolkit';
import { RootState } from '..';
import { getShowtime } from './AsyncThunks';
import { IBookingPrice, IBookingSeat, IBookingState } from '@/types/Booking';

const initialState: IBookingState = {
	maps: [],
	prices: [],
	room: '',
	showtime: null,
	showtimes: [],
	seats: [],
	timelines: [],
	sneakshows: [],
	errors: [],
};

export const bookingSlice = createSlice({
	name: 'booking',
	initialState,
	reducers: {
		updateSeat: (state, action) => {
			if (state.showtime) {
				if (action.payload.showtime_id === state.showtime.id) {
					const seatId = parseInt(action.payload.seat_id);
					state.maps.forEach((item) => {
						if (item.id === seatId) {
							if (item.status !== action.payload.status) {
								item.status = action.payload.status;
							}
						}
					});
				}
			}
		},
		addShowtime: (state, action) => {
			if (state.showtimes.some((item) => item.id === action.payload.id)) {
				console.log('exist');
			} else {
				state.showtimes.push(action.payload);
			}
		},
		setShowtime: (state, action) => {
			if (state.showtimes.some((item) => item.id === action.payload.id)) {
				console.log('exist');
			} else {
				state.showtimes.push(action.payload);
			}
			state.showtime = action.payload;
		},
		setSeats: (state, action) => {
			state.seats = action.payload;
		},
		addSeat: (state, action) => {
			state.seats.push(action.payload);
			state.errors = [];
		},
		removeSeat: (state, action) => {
			state.seats = state.seats.filter((item) => item.id !== action.payload);
			state.maps.forEach((seat) => {
				if (seat.id === action.payload) {
					seat.status = 'active';
				}
			});
			state.errors = [];
		},
		updateSeatSelected: (state, action) => {
			let seat = state.seats.findIndex(
				(item) => item.id === action.payload.seat,
			);
			state.seats[seat] = {
				...state.seats[seat],
				price_id: action.payload.price_id,
				price: action.payload.price,
			};
		},
		prevSeats: (state, action) => {
			action.payload.forEach((seat: IBookingSeat) => {
				let exist = state.seats.some((item) => item.id === seat.id);
				if (!exist) {
					state.seats.push(seat);
				}
			});
		},
		setTimelines: (state, action) => {
			state.timelines = action.payload;
		},
		resetMap: (state) => {
			state.maps = [];
			state.prices = [];
			state.room = '';
			state.errors = [];
		},
		setSneakShows: (state, action) => {
			state.sneakshows = action.payload;
		},
		setErrors: (state, action) => {
			state.errors = action.payload;
		},
		changePrices: (state, action) => {
			state.seats.forEach((seat: IBookingSeat) => {
				if (state.showtime && state.showtime.id === seat.showtime_id) {
					state.prices.forEach((price: IBookingPrice) => {
						if (
							price.price_for_name === action.payload &&
							price.type_seat_id === seat.type_seat_id
						) {
							seat.price_id = price.id;
							seat.price = price.price;
						}
					});
				}
			});
		},
	},
	extraReducers: (builder) => {
		builder.addCase(getShowtime.fulfilled, (state, action) => {
			if (action.payload) {
				state.maps = action.payload.maps;
				state.prices = action.payload.prices || [];
				state.room = action.payload.room;
			}
		});
	},
});

export const {
	updateSeat,
	addShowtime,
	setShowtime,
	setSeats,
	addSeat,
	removeSeat,
	updateSeatSelected,
	prevSeats,
	setTimelines,
	resetMap,
	setSneakShows,
	setErrors,
	changePrices,
} = bookingSlice.actions;

export const getMaps = (state: RootState) => state.booking.maps;
export const getSeats = (state: RootState) => state.booking.seats;
export const getShowtimes = (state: RootState) => state.booking.showtimes;
export const currentShowtime = (state: RootState) => state.booking.showtime;
export const getTimelines = (state: RootState) => state.booking.timelines;
export const getRoom = (state: RootState) => state.booking.room;
export const getSneakShows = (state: RootState) => state.booking.sneakshows;
export const getErrors = (state: RootState) => state.booking.errors;

export default bookingSlice.reducer;
