{"rustc": 10895048813736897673, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 15657897354478470176, "path": 1126333260287446793, "deps": [[2883436298747778685, "pki_types", false, 954979829807540393], [5491919304041016563, "ring", false, 17994817621532769364], [8995469080876806959, "untrusted", false, 11445016086847988381]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-c5fc0be4b435e631\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}