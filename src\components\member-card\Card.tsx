import React from 'react';
import { View, Text, Image, StyleSheet, ImageBackground } from 'react-native';
import isEqual from 'react-fast-compare';

const cards: any = {
	member: {
		bg: require('@/assets/images/card/card-member.png'),
		label: {
			src: require('@/assets/images/card/member-label.png'),
			style: {
				width: 167,
				height: 25,
			},
		},
	},
	diamond: {
		bg: require('@/assets/images/card/card-diamond.png'),
		label: {
			src: require('@/assets/images/card/diamond-label.png'),
			style: {
				width: 180,
				height: 25,
			},
		},
	},
	vip: {
		bg: require('@/assets/images/card/card-vip.png'),
		label: {
			src: require('@/assets/images/card/vip-label.png'),
			style: {
				width: 120,
				height: 30,
			},
		},
	},
};

type Props = {
	level: string;
	id: string;
	name: string;
	width: number;
	pointLevel: number;
	pointReward: number;
	point?: number;
};

export const Card = React.memo(
	({
		level,
		id,
		name,
		width = 400,
		pointLevel = 0,
		pointReward = 0,
		point = 0,
	}: Props) => {
		level = level.toString().toLowerCase().replace('online', 'member');

		const card = cards[level];

		const cardH = width * 0.59;

		return (
			<ImageBackground
				source={card.bg}
				style={[
					styles.card_bg,
					{
						width: width,
						height: cardH,
					},
				]}>
				<View style={styles.card_detail}>
					<Text style={styles.card_id} numberOfLines={1}>
						{name}
					</Text>
					<View style={styles.card_label}>
						<Image source={card.label.src} style={card.label.style} />
					</View>
					<Text style={styles.id}>
						Mã số thẻ: <Text style={styles.card_id}>{id}</Text>
					</Text>
					<Text style={styles.pointLabel}>
						Điểm đổi thưởng: <Text style={styles.point}>{pointReward}</Text>
						{!!point && point > 0 && (
							<Text style={styles.bonus}> +{Math.round(point)}</Text>
						)}
					</Text>
					<Text style={styles.pointLabel}>
						Điểm thăng hạng: <Text style={styles.point}>{pointLevel}</Text>
						{!!point && point > 0 && (
							<Text style={styles.bonus}> +{Math.round(point)}</Text>
						)}
					</Text>
				</View>
			</ImageBackground>
		);
	},
	isEqual,
);

Card.displayName = 'Card';

const styles = StyleSheet.create({
	card_bg: {
		position: 'relative',
	},
	card_label: {
		marginVertical: 5,
	},
	qrcode: {
		backgroundColor: '#fff',
		width: 80,
		height: 80,
		padding: 5,
		position: 'absolute',
		bottom: 15,
		right: 15,
	},
	card_detail: {
		position: 'absolute',
		bottom: 8,
		left: 15,
	},
	id: {
		fontSize: 13,
		color: '#fff',
	},
	card_id: {
		fontSize: 18,
		color: '#fff',
		textTransform: 'uppercase',
	},
	point: {
		color: '#fff',
		fontSize: 18,
	},
	bonus: {
		fontSize: 18,
		color: 'green',
		fontWeight: 'bold',
	},
	pointLabel: {
		color: '#fff',
		fontSize: 13,
		lineHeight: 17,
	},
});

export default Card;
