﻿import React, { memo } from 'react';
import {
	Text,
	View,
	FlatList,
	TouchableOpacity,
	StyleSheet,
} from 'react-native';
import { useSelector } from 'react-redux';
import Icon from '@expo/vector-icons/Ionicons';

import { setGifts } from '@/store/cart/Slice';
import Separator from '@/components/Separator';
import Action from '../Action';
import { RootState, useAppDispatch } from '@/store';
import { IGift } from '@/types/Cart';
import primary from '@/constants/Colors';
import Item from './Item';

const Sidebar = memo(() => {
	const gifts = useSelector((state: RootState) => state.cart.gifts);

	const dispatch = useAppDispatch();

	const reset = () => {
		dispatch(setGifts([]));
	};

	const renderItem = ({ item }: { item: IGift }) => <Item data={item} />;

	return (
		<View style={styles.container}>
			<View style={styles.header}>
				<View style={styles.headerTitle}>
					<Text style={styles.title}>ĐANG CHỌN ({gifts.length})</Text>
				</View>
				<TouchableOpacity style={styles.reset} onPress={reset}>
					<Icon name="trash" size={16} />
					<Text style={styles.resetText}>Chọn lại</Text>
				</TouchableOpacity>
			</View>
			<FlatList
				data={gifts}
				keyExtractor={(item) => item.id.toString()}
				renderItem={renderItem}
				style={styles.list}
				ItemSeparatorComponent={Separator}
			/>
			<Action />
		</View>
	);
});
Sidebar.displayName = 'ExchangeSidebar';

const styles = StyleSheet.create({
	container: {
		height: '100%',
		paddingHorizontal: 12,
	},
	header: {
		flexDirection: 'row',
		height: 60,
		alignItems: 'center',
	},
	headerTitle: {
		flex: 1,
	},
	reset: {
		width: 90,
		flexDirection: 'row',
		alignItems: 'center',
	},
	resetText: {
		color: '#000',
		fontSize: 16,
	},
	list: {},
	title: {
		color: primary,
		fontSize: 20,
	},
});

export default Sidebar;
