import React from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';
import { ShowtimeProps } from '../types';

const Showtime = React.memo(({ showtime, cellWidth }: ShowtimeProps) => {
	const parseTime = (time: string): number => {
		const parse = time.split(':');
		return parseInt(parse[0]) * 60 + parseInt(parse[1]);
	};

	const toPix = (min: number): number => {
		return (min / 60) * cellWidth;
	};

	let style: ViewStyle = {};
	let showtime_to = parseTime(showtime.to);
	let showtime_from = parseTime(showtime.from);
	if (showtime_to < showtime_from) {
		showtime_to = showtime_to + 1440;
	}
	style.width = toPix(showtime_to - showtime_from);
	style.left = toPix(showtime_from) - cellWidth * 7;
	style.backgroundColor = showtime.color;

	return (
		<View style={styles.showtime}>
			<View style={[styles.showtimeBtn, style]}>
				<Text style={styles.movie} numberOfLines={3}>
					{showtime.movie} {showtime.movie_age}
				</Text>
				<Text style={styles.time}>
					{showtime.from} - {showtime.to}
				</Text>
				<Text style={styles.booked}>
					{showtime.booked}/{showtime.total}
				</Text>
			</View>
		</View>
	);
});

Showtime.displayName = 'Showtime';

const styles = StyleSheet.create({
	showtime: {
		position: 'absolute',
		paddingVertical: 7,
		height: '100%',
	},
	showtimeBtn: {
		borderRadius: 5,
		padding: 3,
		height: '100%',
		alignItems: 'center',
		justifyContent: 'center',
		borderWidth: 1,
		borderColor: '#ddd',
	},
	movie: {
		fontSize: 14,
		textAlign: 'center',
		color: '#fff',
		fontWeight: 'bold',
	},
	time: {
		fontSize: 13,
		color: '#fff',
	},
	booked: {
		color: '#fff',
		fontWeight: 'bold',
	},
});

export default Showtime;
