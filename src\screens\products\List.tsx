import React, { memo, useState } from 'react';
import {
	View,
	StyleSheet,
	ActivityIndicator,
	LayoutChangeEvent,
	Text,
} from 'react-native';
import { useSelector } from 'react-redux';
import { TabBar, TabView } from 'react-native-tab-view';

import { getGroup } from '@/store/Product/Selector';
import SearchProduct from './components/SearchProduct';
import ProductGroup from './components/ProductGroup';
import type { IProductGroup } from '@/types/Product';
import primary from '@/constants/Colors';

interface Route {
	key: string;
	title: string;
}

type RouteLabelProps = {
	route: Route;
	focused: boolean;
	color: string;
};

const List = memo(({ ready }: { ready: boolean }) => {
	const data = useSelector(getGroup);

	const [viewWitdh, setViewWidth] = useState<number>(0);

	const [active, setActive] = useState<number>(0);

	const tabs = data.map((item: IProductGroup): Route => {
		return {
			key: item.id.toString(),
			title: item.name,
		};
	});

	const renderScene = ({ route }: { route: Route }) => {
		return <ProductGroup id={parseInt(route.key)} />;
	};

	const renderTabBar = (props: any) => (
		<TabBar {...props} style={styles.tabbar} renderLabel={renderLabel} />
	);

	const renderLabel = ({ route, focused, color }: RouteLabelProps) => (
		<Text style={[styles.label, { color }]}>{route.title}</Text>
	);

	const onLayout = (event: LayoutChangeEvent) => {
		const { width } = event.nativeEvent.layout;
		setViewWidth(width);
	};

	return (
		<View style={styles.wrap}>
			<SearchProduct />
			<View style={styles.tabs} onLayout={onLayout}>
				{!ready && (
					<ActivityIndicator
						size="large"
						color={primary}
						style={styles.loading}
					/>
				)}
				{ready && viewWitdh > 0 && data.length > 0 && (
					<TabView
						swipeEnabled={false}
						navigationState={{ index: active, routes: tabs }}
						renderScene={renderScene}
						renderTabBar={renderTabBar}
						onIndexChange={setActive}
						initialLayout={{ width: viewWitdh }}
					/>
				)}
			</View>
		</View>
	);
});
List.displayName = 'ListProduct';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
	},
	tabs: {
		flex: 1,
		paddingHorizontal: 10,
	},
	tabbar: {
		backgroundColor: primary,
	},
	label: {
		fontWeight: 'bold',
		fontSize: 16,
		lineHeight: 18,
		textTransform: 'uppercase',
		opacity: 1,
	},
	loading: {
		marginTop: '20%',
	},
});

export default List;
